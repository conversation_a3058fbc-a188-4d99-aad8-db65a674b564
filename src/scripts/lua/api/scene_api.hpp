#ifndef __IF__SCENE_API_HPP
#define __IF__SCENE_API_HPP

// C++ standard library
#include <string>

// Third-party libraries
#include <sol/sol.hpp>

// Local includes
#include "../../../scene/scene.hpp"
#include "../../../scene/collision/scene_collision_manager.hpp"

namespace IronFrost {
  class GameScene; // Forward declaration

  namespace API {
    void bindSceneAPI(sol::state& luaState) {
      luaState.new_usertype<Camera>("Camera",
        "setPosition", &Camera::setPosition,
        "setDirection", &Camera::setDirection,
        "setUp", &Camera::setUp,
        "setFov", &Camera::setFov,
        "setAspectRatio", &Camera::setAspectRatio,
        "setNear", &Camera::setNearPlane,
        "setFar", &Camera::setFarPlane,
        "moveForward", &Camera::moveForward,
        "moveBackward", &Camera::moveBackward,
        "moveLeft", &Camera::moveLeft,
        "moveRight", &Camera::moveRight,
        "turnLeft", &Camera::turnLeft,
        "turnRight", &Camera::turnRight,
        "turnUp", &Camera::turnUp,
        "turnDown", &Camera::turnDown,
        "mouseLookYaw", &Camera::mouseLookYaw,
        "mouseLookPitch", &Camera::mouseLookPitch,
        "calculateForwardMovement", &Camera::calculateForwardMovement,
        "calculateBackwardMovement", &Camera::calculateBackwardMovement,
        "calculateLeftMovement", &Camera::calculateLeftMovement,
        "calculateRightMovement", &Camera::calculateRightMovement,
        "setPositionIfValid", &Camera::setPositionIfValid,
        "getCollisionRadius", &Camera::getCollisionRadius,
        "setCollisionRadius", &Camera::setCollisionRadius);

      luaState.new_usertype<SceneCollisionManager>("SceneCollisionManager",
        "checkPointCollision", [](SceneCollisionManager& manager, const glm::vec3& point) -> sol::optional<glm::vec3> {
          auto result = manager.checkPointCollision(point);
          if (result.has_value()) {
            return sol::make_optional(result.value());
          }
          return sol::nullopt;
        },
        "checkSphereCollision", [](SceneCollisionManager& manager, const glm::vec3& center, float radius) -> sol::optional<glm::vec3> {
          auto result = manager.checkSphereCollision(center, radius);
          if (result.has_value()) {
            return sol::make_optional(result.value());
          }
          return sol::nullopt;
        },
        "resolveSphereCollision", &SceneCollisionManager::resolveSphereCollision,
        "getClosestSurfacePoint", &SceneCollisionManager::getClosestSurfacePoint,
        "getCollisionShapeCount", &SceneCollisionManager::getCollisionShapeCount);

      luaState.new_usertype<SceneNode>("SceneNode",
        "getPosition", &SceneNode::getPosition,
        "setPosition", &SceneNode::setPosition,
        "getRotation", &SceneNode::getRotation,
        "setRotation", &SceneNode::setRotation,
        "getScale", &SceneNode::getScale,
        "setScale", &SceneNode::setScale);

      luaState.new_usertype<ISceneGraph>("SceneGraph",
        "get", &ISceneGraph::get);

      luaState.new_usertype<IScene>("Scene",
        "isLoading", &IScene::isLoading,
        "setPostprocessEffect", [](IScene& _scene, const std::string& _name) {
          auto& sceneRenderer = _scene.getSceneRenderer();
          return sceneRenderer.setPostprocessEffect(StringID(_name));
        },
        "getCamera", &IScene::getCamera,
        "getGUI", &IScene::getGUI,
        "getCollisionManager", [](IScene& _scene) -> SceneCollisionManager& {
          // Cast to GameScene to access collision manager
          auto* gameScene = dynamic_cast<GameScene*>(&_scene);
          if (!gameScene) {
            throw std::runtime_error("Collision manager only available for GameScene");
          }
          return gameScene->getCollisionManager();
        },
        "getSceneGraph", [](IScene& _scene) -> ISceneGraph& {
          // Cast to GameScene to access scene graph
          auto* gameScene = dynamic_cast<GameScene*>(&_scene);
          if (!gameScene) {
            throw std::runtime_error("Scene graph only available for GameScene");
          }
          return gameScene->getSceneGraph();
        },
        "getSceneName", &IScene::getSceneName);
    }
  }
}

#endif
