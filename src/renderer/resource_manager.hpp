#ifndef __IF__RESOURCE_MANAGER_HPP
#define __IF__RESOURCE_MANAGER_HPP

// C++ standard library
#include <exception>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

// Local includes
#include "gpu_handles.hpp"
#include "material.hpp"
#include "material_library.hpp"
#include "../assets/asset_data_types.hpp"
#include "../events/event_dispatcher.hpp"
#include "../services/service_locator.hpp"
#include "../services/resource_orchestrator/fallback_resources.hpp"
#include "../services/resource_orchestrator/resource_events.hpp"
#include "../services/resource_orchestrator/resource_type.hpp"
#include "../utils/containers_set.hpp"

namespace IronFrost {
  struct MeshData;
  struct ImageData;
  struct FontData; 
  struct ModelData;

  class IResourceManager {
    protected:
      ValueContainersSet<
        MeshHandle,
        TextureHandle,
        ModelH<PERSON>le,
        FontHandle,
        ShaderHandle
      > m_resourceHandles;

      MaterialLibrary m_materialLibrary;

      void registerListeners() {
        EventDispatcher& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

        eventDispatcher.registerListener<LoadMeshEvent>([&](const LoadMeshEvent& event) {
          createMesh(event.name(), event.meshData());
          event.callCallback();
        });

        eventDispatcher.registerListener<LoadModelEvent>([&](const LoadModelEvent& event) {
          createModel(event.name(), event.modelData());
          event.callCallback();
        });

        eventDispatcher.registerListener<LoadTerrainEvent>([&](const LoadTerrainEvent& event) {
          const TerrainData& terrainData = event.terrainData();

          createMesh(event.name(), terrainData.meshData);
          event.callCallback();
        });

        eventDispatcher.registerListener<LoadShaderEvent>([&](const LoadShaderEvent& event) {
          createShader(event.name(), event.shaderData().vertexShader, event.shaderData().fragmentShader);
          event.callCallback();
        });

        eventDispatcher.registerListener<LoadTextureEvent>([&](const LoadTextureEvent& event) {
          createTexture(event.name(), event.imageData());
          event.callCallback();
        });

        eventDispatcher.registerListener<LoadFontEvent>([&](const LoadFontEvent& event) {
          createFont(event.name(), event.fontData());
          event.callCallback();
        });

        eventDispatcher.registerListener<LoadMaterialEvent>([&](const LoadMaterialEvent& event) {
          createMaterial(event.name(), event.materialData());
          event.callCallback();
        });
      }

    public:
      IResourceManager() {
        registerListeners();
      };
      virtual ~IResourceManager() = default;

      template<typename T>
      void insert(const StringID& name, const T& handle) {
        m_resourceHandles.getContainer<T>().insert(name, handle);
      }

      template<typename T>
      const T& get(const StringID& name) const {
        return m_resourceHandles.getContainer<T>().get(name);
      }

      template<typename T>
      bool has(const StringID& name) const {
        return m_resourceHandles.getContainer<T>().has(name);
      }

      template<typename T>
      void remove(const StringID& name) {
        m_resourceHandles.getContainer<T>().remove(name);
      }

      template<typename T>
      std::optional<StringID> findID(const T& handler) const {
        return m_resourceHandles.getContainer<T>().findID(handler);
      }

      virtual MeshHandle createMesh(const MeshData& meshData) = 0;
      virtual const MeshHandle& createMesh(const StringID& name, const MeshData& meshData) = 0;
      virtual void destroyMesh(const MeshHandle& meshHandle) = 0;
      virtual void destroyMesh(const StringID& name) = 0;

      virtual TextureHandle createTexture(const ImageData& imageData) = 0;
      virtual const TextureHandle& createTexture(const StringID& name, const ImageData& imageData) = 0;
      virtual TextureHandle createTextureArray(const std::vector<std::unique_ptr<ImageData>>& imageDataArray) = 0;
      virtual const TextureHandle& createTextureArray(const StringID& name, const std::vector<std::unique_ptr<ImageData>>& imageDataArray) = 0;
      virtual void destroyTexture(const TextureHandle& textureHandle) = 0;
      virtual void destroyTexture(const StringID& name) = 0;

      virtual const ModelHandle& createModel(const StringID& name, const ModelData& modelData) = 0;
      virtual void destroyModel(const ModelHandle& modelHandle) = 0;
      virtual void destroyModel(const StringID& name) = 0;

      virtual MeshHandle createTerrain(const StringID& name, const HeightmapData& heightmapData) = 0;
      virtual void destroyTerrain(const StringID& name) = 0;

      virtual const FontHandle& createFont(const StringID& name, const FontData& fontData) = 0;
      virtual void destroyFont(const FontHandle& fontHandle) = 0;
      virtual void destroyFont(const StringID& name) = 0;

      virtual const ShaderHandle& createShader(const StringID& name, const std::string& vertexShaderSource, const std::string& fragmentShaderSource) = 0;
      virtual void destroyShader(const ShaderHandle& shaderHandle) = 0;
      virtual void destroyShader(const StringID& name) = 0;

      const Material& createMaterial(const StringID& name, const MaterialData& materialData) {
        if (const BlinnPhongMaterialData* data = std::get_if<BlinnPhongMaterialData>(&materialData.data)) {
          BlinnPhongMaterial blinnPhongMaterial;
          
          blinnPhongMaterial.ambient = data->ambient;
          blinnPhongMaterial.diffuse = data->diffuse;
          blinnPhongMaterial.specular = data->specular;
          blinnPhongMaterial.shininess = data->shininess;

          if (data->textureName != StringID("")) {
            blinnPhongMaterial.baseColor = get<TextureHandle>(data->textureName);
          }

          addMaterial(name, blinnPhongMaterial);
        } else if (const PBRMaterialData* data = std::get_if<PBRMaterialData>(&materialData.data)) {
          PBRMaterial pbrMaterial;
          pbrMaterial.pbrTextureArray = createTextureArray(data->textureArray);

          addMaterial(name, pbrMaterial);
        }

        return m_materialLibrary.getMaterial(name);
      }

      void addMaterial(const StringID& name, const Material& material) {
        m_materialLibrary.addMaterial(name, material);
      }

      const Material& getMaterial(const StringID& name) const {
        return m_materialLibrary.getMaterial(name);
      }

      void destroyResource(const StringID& name, ResourceType type) {
        switch (type) {
          case ResourceType::MESH:
            return destroyMesh(name);
          case ResourceType::TEXTURE:
            return destroyTexture(name);
          case ResourceType::MODEL:
            return destroyModel(name);
          case ResourceType::FONT:
            return destroyFont(name);
          case ResourceType::SHADER:
            return destroyShader(name);
          default:
            return;
        }
      }
  };
}

#endif
