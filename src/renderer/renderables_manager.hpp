#ifndef __IF__RENDERABLES_MANAGER_HPP
#define __IF__RENDERABLES_MANAGER_HPP

// C++ standard library
#include <functional>
#include <iostream>
#include <unordered_map>

// Third-party libraries
#define GLM_ENABLE_EXPERIMENTAL
#include <glm/glm.hpp>
#include <glm/gtx/string_cast.hpp>

// Local includes
#include "../assets/asset_data_types.hpp"
#include "../utils/storage.hpp"
#include "renderables/renderables.hpp"
#include "resource_manager.hpp"

namespace IronFrost {
  class RenderablesManager {
    private:
      std::unordered_map<StringID, RenderableModelID> m_renderableModels;
      Storage<RenderableModelID, RenderableModel> m_renderableModelStorage;
      
      std::unordered_map<StringID, RenderableObjectID> m_renderableObjects;
      Storage<RenderableObjectID, RenderableObject> m_renderableObjectStorage;

      const IResourceManager& m_resourceManager;

    public:
      RenderablesManager(const IResourceManager& resourceManager) :
        m_resourceManager(resourceManager) 
      {}

      RenderableObjectID createRenderableObject(
        const ShaderHandle& shaderHandle, 
        const MeshHandle& meshHandle, 
        const Material& material,
        const ShaderUniforms& uniforms = ShaderUniforms()
      ) {
        RenderableObject renderableObject{shaderHandle, meshHandle, material, uniforms};
        return m_renderableObjectStorage.insert(renderableObject);
      }

      RenderableObjectID createRenderableObject(
        const StringID& shaderName,
        const StringID& meshName,
        const StringID& materialName,
        const ShaderUniforms& uniforms = ShaderUniforms()
      ) {
        const ShaderHandle& shaderHandle = m_resourceManager.get<ShaderHandle>(shaderName);
        const MeshHandle& meshHandle = m_resourceManager.get<MeshHandle>(meshName);
        const Material& material = m_resourceManager.getMaterial(materialName);

        return createRenderableObject(shaderHandle, meshHandle, material, uniforms);
      }

      RenderableObjectID createRenderableObjectWithName(
        const StringID& name,
        const ShaderHandle& shaderHandle,
        const MeshHandle& meshHandle,
        const Material& material,
        const ShaderUniforms& uniforms = ShaderUniforms()
      ) {
        RenderableObjectID id = createRenderableObject(shaderHandle, meshHandle, material, uniforms);
        m_renderableObjects.emplace(name, id);
        return id;
      }

      RenderableObjectID createRenderableObjectWithName(
        const StringID& name,
        const StringID& shaderName,
        const StringID& meshName,
        const StringID& materialName,
        const ShaderUniforms& uniforms = ShaderUniforms()
      ) {
        RenderableObjectID id = createRenderableObject(shaderName, meshName, materialName, uniforms);
        m_renderableObjects.emplace(name, id);
        return id;
      }

      void destroyRenderableObject(RenderableObjectID id) {
        m_renderableObjectStorage.destroy(id);
      }

      void destroyRenderableObject(const StringID& name) {
        auto it = m_renderableObjects.find(name);
        if (it != m_renderableObjects.end()) {
          m_renderableObjectStorage.destroy(it->second);
          m_renderableObjects.erase(it);
        }
      }

      RenderableObjectID getRenderableObjectID(const StringID& name) const {
        auto it = m_renderableObjects.find(name);
        if (it != m_renderableObjects.end()) {
          return it->second;
        }

        std::cerr << "Renderable object " << StringID::getString(name) << " not found!" << std::endl;
        return -1;
      }

      RenderableObject* getRenderableObject(RenderableObjectID id) {
        return m_renderableObjectStorage.get(id);
      }

      RenderableObject* getRenderableObject(const StringID& name) {
        auto it = m_renderableObjects.find(name);
        if (it != m_renderableObjects.end()) {
          return m_renderableObjectStorage.get(it->second);
        }

        return nullptr;
      }

      const RenderableObject* getRenderableObject(RenderableObjectID id) const {
        return m_renderableObjectStorage.get(id);
      }

      const RenderableObject* getRenderableObject(const StringID& name) const {
        auto it = m_renderableObjects.find(name);
        if (it != m_renderableObjects.end()) {
          return m_renderableObjectStorage.get(it->second);
        }

        return nullptr;
      }

      RenderableModelID createRenderableModel(
        const ShaderHandle& shaderHandle, 
        const ModelHandle& modelHandle,
        const ShaderUniforms& uniforms = ShaderUniforms()
      ) {
        RenderableModel renderableModel;

        std::function<RenderableModel::Node(const ModelHandle::Node&)> processNode = [&](const ModelHandle::Node& node) {
          RenderableModel::Node modelNode;
          modelNode.transform = node.transform;

          std::cout << "Meshes count: " << node.meshes.size() << std::endl;
          std::cout << "Textures count: " << node.textures.size() << std::endl;

          for (int i = 0; i < node.meshes.size(); ++i) {
            BlinnPhongMaterial material; 
            material.baseColor = node.textures[i];

            modelNode.objects.push_back(
              createRenderableObject(shaderHandle, node.meshes[i], material, uniforms)
            );

            std::cout << "Mesh: " << node.meshes[i].VAO << std::endl;
            std::cout << "Texture: " << node.textures[i].textureID << std::endl;
          }

          for (const ModelHandle::Node& child : node.children) {
            modelNode.children.push_back(processNode(child));
          }

          return modelNode;
        };

        renderableModel.root = processNode(modelHandle.rootNode);

        return m_renderableModelStorage.insert(renderableModel);
      }

      void destroyRenderableModel(RenderableModelID id) {
        m_renderableModelStorage.destroy(id);
      }
      
      RenderableModel* getRenderableModel(RenderableModelID id) {
        return m_renderableModelStorage.get(id);
      }

      const RenderableModel* getRenderableModel(RenderableModelID id) const {
        return m_renderableModelStorage.get(id);
      }

      void clearAll() {
        m_renderableModels.clear();
        m_renderableModelStorage.clear();

        m_renderableObjects.clear();
        m_renderableObjectStorage.clear();
      }
  };
}

#endif
