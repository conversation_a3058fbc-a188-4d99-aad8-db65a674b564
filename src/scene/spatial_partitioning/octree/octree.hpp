#ifndef __IF__OCTREE_HPP
#define __IF__OCTREE_HPP

// C++ standard library
#include <memory>
#include <vector>

// Local includes
#include "../spatial_partitioning.hpp"
#include "octree_node.hpp"

namespace IronFrost {
  class Octree : public ISpatialPartitioning {
    private:
      std::unique_ptr<OctreeNode> m_root;

    public:
      Octree(const CollisionMath::AABB& bounds) {
        m_root = std::make_unique<OctreeNode>(bounds, 0);
      }

      ~Octree() override = default;

      void insert(SceneNode* node, const CollisionMath::AABB& bounds) override {
        m_root->insert(node, bounds);
      }

      void update(SceneNode* node, const CollisionMath::AABB& bounds) override {
        remove(node);
        insert(node, bounds);
      }

      void remove(SceneNode* node) override {
        m_root->remove(node);
      }

      void collectVisible(const Frustum& frustum, std::function<void(SceneNode*, const glm::mat4&)> callback) const override {
        m_root->collectVisible(frustum, callback);
      }
  };
}

#endif
