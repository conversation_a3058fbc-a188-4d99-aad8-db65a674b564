#ifndef __IF__OCTREE_NODE_HPP
#define __IF__OCTREE_NODE_HPP

// C++ standard library
#include <memory>
#include <vector>
#include <algorithm>

// Local includes
#include "../../../utils/collision_math.hpp"
#include "../../scene_graph/scene_node.hpp"

namespace IronFrost {
  class OctreeNode {
    private:
      CollisionMath::AABB m_bounds;
      int m_depth;

      std::vector<std::pair<SceneNode*, CollisionMath::AABB>> m_objects;
      std::unique_ptr<OctreeNode> m_children[8];

      bool isLeaf() const {
        return m_children[0] == nullptr;
      }

      bool shouldSubdivide() const {
        return isLeaf() && m_depth < MaxDepth && m_objects.size() > MaxObjectsPerNode;
      }

      void subdivide() {
        auto childrenBounds = CollisionMath::subdivideAABB(m_bounds);
        for (int i = 0; i < 8; ++i) {
          m_children[i] = std::make_unique<OctreeNode>(childrenBounds[i], m_depth + 1);
        }
      }

      bool tryInsertIntoChildren(SceneNode* node, const CollisionMath::AABB& bounds) {
        if (isLeaf()) {
          return false;
        }

        for (auto& child : m_children) {
          if (child->m_bounds.contains(bounds)) {
            child->insert(node, bounds);
            return true;
          }
        }
        return false;
      }

      void trySubdivideAndDistribute() {
        if (!shouldSubdivide()) {
          return;
        }

        subdivide();
        std::vector<std::pair<SceneNode*, CollisionMath::AABB>> leftover;

        for (const auto& [obj, objBounds] : m_objects) {
          if (!tryInsertIntoChildren(obj, objBounds)) {
            leftover.emplace_back(obj, objBounds);
          }
        }

        m_objects = std::move(leftover);
      }

    public:
      static constexpr int MaxObjectsPerNode = 8;
      static constexpr int MaxDepth = 8;

      OctreeNode(const CollisionMath::AABB& bounds, int depth) : m_bounds(bounds), m_depth(depth) {}
      ~OctreeNode() = default;

      void insert(SceneNode* node, const CollisionMath::AABB& bounds) {
        if (!CollisionMath::aabbIntersection(m_bounds, bounds)) {
          return;
        }

        if (tryInsertIntoChildren(node, bounds)) { 
          return;
        }
        
        m_objects.emplace_back(node, bounds);

        trySubdivideAndDistribute();
      }

      void remove(SceneNode* node) {
        auto it = std::remove_if(m_objects.begin(), m_objects.end(), [&node](const auto& pair) {
          return pair.first == node;
        });

        m_objects.erase(it, m_objects.end());

        if (!isLeaf()) {
          for (auto& child : m_children) {
            child->remove(node);
          }
        }
      }

      void collectVisible(const Frustum& frustum, std::function<void(SceneNode*, const glm::mat4&)> callback) const {
        if (!frustum.intersects(m_bounds)) {
          return;
        }

        for (const auto& [node, aabb] : m_objects) {
          if (frustum.intersects(aabb)) {
            callback(node, node->getTransform());
          }
        }

        for (const auto& child : m_children) {
          if (child) {
            child->collectVisible(frustum, callback);
          }
        }
      }
  };
}

#endif
