#ifndef __IF__SPATIAL_PARTITIONING_HPP
#define __IF__SPATIAL_PARTITIONING_HPP

// C++ standard library
#include <memory>
#include <functional>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../../utils/collision_math.hpp"
#include "../camera/frustum.hpp"

namespace IronFrost {
  class SceneNode;

  enum class SPATIAL_PARTITIONING_TYPE {
    OCTREE
  };

  class ISpatialPartitioning {
    public:
      virtual ~ISpatialPartitioning() = default;

      virtual void insert(SceneNode* node, const CollisionMath::AABB& bounds) = 0;
      virtual void update(SceneNode* node, const CollisionMath::AABB& bounds) = 0;
      virtual void remove(SceneNode* node) = 0;

      virtual void collectVisible(const Frustum& frustum, std::function<void(SceneNode*, const glm::mat4&)> callback) const = 0;

      static std::unique_ptr<ISpatialPartitioning> create(SPATIAL_PARTITIONING_TYPE spatialPartitioningType);
  };
}

#endif
