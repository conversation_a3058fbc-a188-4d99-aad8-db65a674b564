#include "scene.hpp"

// C++ standard library
#include <iostream>
#include <memory>
#include <string>
#include <utility>

// Local includes
#include "../assets/assets_manager.hpp"
#include "../assets/assets_loader.hpp"
#include "../events/event_dispatcher.hpp"
#include "../renderer/gui_renderer.hpp"
#include "../renderer/postprocess_effect.hpp"
#include "../renderer/renderer.hpp"
#include "../renderer/resource_manager.hpp"
#include "../scripts/script_context.hpp"
#include "../scripts/script_engine.hpp"
#include "../services/service_locator.hpp"
#include "../utils/game_context.hpp"
#include "../window/window.hpp"
#include "loaders/scene_loader.hpp"
#include "scene_graph/scene_list.hpp"
#include "scene_context.hpp"

namespace IronFrost {
  void IScene::setPostprocessUniforms() {
    const auto& window = m_sceneContext.renderer.getWindow();

    ShaderUniforms postprocessUniforms;

    postprocessUniforms.set("windowSize", window.getSize());
    postprocessUniforms.set("blockSize", 4.0F);
    postprocessUniforms.set("aberrationAmount", 3.0F);

    m_sceneRenderer.setPostprocessUniforms(postprocessUniforms);
  }

  void IScene::registerEventListeners() {
    ServiceLocator::getService<EventDispatcher>().registerListener<WindowResizeEvent>(
      [&](const WindowResizeEvent &event) {
        m_camera.setAspectRatio(event.getWidth() / event.getHeight());
        m_sceneContext.renderer.getMultipassFramebuffer().update(event.getWidth(), event.getHeight());
        setPostprocessUniforms();
      });
  }

  IScene::IScene(const StringID& sceneName, std::string scenePath, SceneContext& sceneContext) :
    m_scenePath(std::move(scenePath)),
    m_sceneName(sceneName),
    m_sceneContext(sceneContext),
    m_sceneRenderer(m_sceneContext.renderer)
  {
    const auto& window = sceneContext.renderer.getWindow();

    m_camera.setAspectRatio(window.getWidth() / window.getHeight());

    setPostprocessUniforms();
    registerEventListeners();
  }

  bool IScene::isLoading() const {
    return m_isLoading;
  }

  Camera& IScene::getCamera() {
    return m_camera;
  }

  GUI& IScene::getGUI() {
    return m_gui;
  }

  SceneRenderer& IScene::getSceneRenderer() {
    return m_sceneRenderer;
  }

  const StringID& IScene::getSceneName() const {
    return m_sceneName;
  }

  void GameScene::handleInput(float deltaTime, const GameContext& gameContext) {
    // Camera movement is handled in Lua scripts
  }
  
  void GameScene::loadScene(const std::string& path) {
    SceneLoader sceneLoader(m_sceneContext, path);

    sceneLoader.loadObjects(m_sceneContext.renderer);
    sceneLoader.loadLighting(m_sceneLighting);

    sceneLoader.loadScene(m_sceneContext.renderer,
      [&](const StringID& name, SceneNode&& sceneNode) {
        m_sceneGraph->insert(name, std::move(sceneNode));
      });

    sceneLoader.loadGUI(
      [&](const StringID& name, std::unique_ptr<Widget> widget) {
        m_gui.addWidget(name, std::move(widget));
      });
  }

  void GameScene::unloadScene() {
    auto& renderablesManager = m_sceneContext.renderer.getRenderablesManager();

    renderablesManager.clearAll();
  }

  void GameScene::loadSceneScripts(const std::string& path) {
    if (!m_scriptContext) {
      std::cout << "  Warning: Script context not available for scene scripts" << '\n';
      return;
    }

    SceneLoader sceneLoader(m_sceneContext, path);

    std::cout << "  Loading scene scripts for: " << StringID::getString(m_sceneName) << '\n';

    // Load and execute scene init scripts
    auto initScriptContents = sceneLoader.getSceneInitScriptContents();
    if (!initScriptContents.empty()) {
      m_scriptContext->executeInitScripts(initScriptContents);
    }

    // Load and register scene per-frame scripts
    auto perFrameScriptContents = sceneLoader.getScenePerFrameScriptContents();
    if (!perFrameScriptContents.empty()) {
      m_scriptContext->registerPerFrameScripts(perFrameScriptContents);
    }
  }

  void GameScene::unloadSceneScripts() {
    if (!m_scriptContext) {
      return;
    }

    std::cout << "  Unloading scene scripts for: " << StringID::getString(m_sceneName) << '\n';
    // Scene context will be automatically destroyed with the unique_ptr
  }

  void GameScene::executePerFrameScripts(float deltaTime) {
    if (m_scriptContext) {
      m_scriptContext->executePerFrame(deltaTime);
    }
  }

  SceneCollisionManager& GameScene::getCollisionManager() {
    if (!m_collisionManager) {
      throw std::runtime_error("Collision manager not initialized");
    }
    return *m_collisionManager;
  }

  ISceneGraph & GameScene::getSceneGraph() {
    if (!m_sceneGraph) {
      throw std::runtime_error("Scene graph not initialized");
    }
    return *m_sceneGraph;
  }

  void GameScene::setUniforms() {
    m_sceneContext.renderer.setGlobalUniform("ambientLight.color", m_sceneLighting.getAmbientLightColor());
    m_sceneContext.renderer.setGlobalUniform("ambientLight.intensity", m_sceneLighting.getAmbientLightIntensity());

    const auto& directionalLights = m_sceneLighting.getDirectionalLights();
    int numDirectionalLights = static_cast<int>(directionalLights.size()) > MAX_DIRECTIONAL_LIGHTS ? MAX_DIRECTIONAL_LIGHTS : static_cast<int>(directionalLights.size());

    m_sceneContext.renderer.setGlobalUniform("numDirectionalLights", numDirectionalLights);

    for(int i = 0; i < numDirectionalLights; i++) {
      const auto& light = directionalLights[i];

      m_sceneContext.renderer.setGlobalUniform("dirLights[" + std::to_string(i) + "].direction", light.direction);
      m_sceneContext.renderer.setGlobalUniform("dirLights[" + std::to_string(i) + "].color", light.color);
      m_sceneContext.renderer.setGlobalUniform("dirLights[" + std::to_string(i) + "].intensity", light.intensity);
    }

    m_sceneContext.renderer.setGlobalUniform("cameraPosition", m_camera.getPosition());
  }

  void GameScene::updateSpatialPartitioning() {
    m_sceneGraph->traverseDirty(
      [&](SceneNode& sceneNode, const glm::mat4& transform) {
        if (sceneNode.sceneObject || sceneNode.sceneModel) {
          auto bounds = sceneNode.getWorldBounds(transform);
          if (bounds) {
            m_spatialPartitioning->update(&sceneNode, bounds.value());
          }
        }
      });
  }

  GameScene::GameScene(const StringID& sceneName, const std::string& scenePath, SceneContext& sceneContext) :
    IScene(sceneName, scenePath, sceneContext),
    m_sceneAssets(sceneContext)
  {
    if (sceneContext.scriptEngine) {
      // Create scene script context with this scene
      m_scriptContext = sceneContext.scriptEngine->createSceneScriptContext(*this);
    }

    // m_sceneRenderer.setPostprocessEffect(m_sceneContext.renderer.getPostprocessEffect(StringID("postprocess::chromatic-aberration")));
  }

  void GameScene::load() {
    std::cout << "Creating scene graph" << '\n';
    m_sceneGraph = ISceneGraph::create(SCENE_GRAPH_TYPE::LIST);
    m_spatialPartitioning = ISpatialPartitioning::create(SPATIAL_PARTITIONING_TYPE::OCTREE);

    std::cout << "Loading scene assets" << '\n';
    m_sceneAssets.load(m_scenePath + "/assets.json");
    std::cout << "Loading scene data" << '\n';
    loadScene(m_scenePath + "/config.json");
    std::cout << "Loading scene scripts" << '\n';
    m_collisionManager = std::make_unique<SceneCollisionManager>(m_sceneGraph.get());
    loadSceneScripts(m_scenePath + "/config.json");

    ServiceLocator::getService<EventDispatcher>().dispatch<SceneLoadedEvent>(this);
  }

  void GameScene::unload() {
    std::cout << "Clearing GUI widgets" << '\n';
    m_gui.clearAllWidgets();
    std::cout << "Unloading scene scripts" << '\n';
    unloadSceneScripts();
    std::cout << "Unloading scene data" << '\n';
    unloadScene();
    m_collisionManager.reset();
    std::cout << "Unloading scene assets" << '\n';
    m_sceneAssets.unload();

    ServiceLocator::getService<EventDispatcher>().dispatch<SceneUnloadedEvent>();
  }

  void GameScene::update(float deltaTime, GameContext& gameContext) {
    ServiceLocator::getService<EventDispatcher>().dispatch<SceneUpdateEvent>();

    handleInput(deltaTime, gameContext);
    updateSpatialPartitioning();

    m_gui.update(deltaTime, gameContext);
  }

  void GameScene::render() {
    setUniforms();

    m_sceneRenderer.render(*m_sceneGraph, *m_spatialPartitioning, m_camera, m_gui);
  }
  
  LoadingScene::LoadingScene(const std::string& scenePath, SceneContext& sceneContext) :
    IScene(StringID("loading"), scenePath, sceneContext)
  {}

  void LoadingScene::load() {}

  void LoadingScene::unload() {}

  void LoadingScene::update(float deltaTime, GameContext& gameContext) {}

  void LoadingScene::render() {
    m_sceneContext.renderer.getGUIRenderer().render(m_gui);
  }


}
