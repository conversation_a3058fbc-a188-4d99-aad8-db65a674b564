#ifndef __IF__COLLISION_SHAPE_HPP
#define __IF__COLLISION_SHAPE_HPP

// C++ standard library
#include <memory>
#include <optional>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../../../utils/collision_math.hpp"

namespace IronFrost {
  
  /**
   * Base class for collision shapes
   * Defines the interface that all collision shapes must implement
   */
  class CollisionShape {
    public:
      virtual ~CollisionShape() = default;
      
      /**
       * Check if a point would collide with this shape
       * Returns the collision normal if collision occurs, nullopt otherwise
       */
      virtual std::optional<glm::vec3> checkPointCollision(const glm::vec3& point) const = 0;
      
      /**
       * Check if a sphere would collide with this shape
       * Returns the collision normal if collision occurs, nullopt otherwise
       */
      virtual std::optional<glm::vec3> checkSphereCollision(const CollisionMath::Sphere& sphere) const = 0;
      
      /**
       * Get the closest point on the surface of this shape to the given point
       */
      virtual glm::vec3 getClosestPoint(const glm::vec3& point) const = 0;
      
      /**
       * Get the distance from a point to this shape's surface
       * Negative distance means the point is inside the shape
       */
      virtual float getDistanceToSurface(const glm::vec3& point) const = 0;
      
      /**
       * Resolve sphere collision by returning corrected sphere center
       * Returns the original center if no collision
       */
      virtual glm::vec3 resolveSphereCollision(const CollisionMath::Sphere& sphere) const = 0;
  };
  
} // namespace IronFrost

#endif
