#ifndef __IF__SPHERE_COLLISION_SHAPE_HPP
#define __IF__SPHERE_COLLISION_SHAPE_HPP

// Local includes
#include "collision_shape.hpp"

namespace IronFrost {
  
  /**
   * Sphere collision shape
   */
  class SphereCollisionShape : public CollisionShape {
    private:
      CollisionMath::Sphere m_sphere;
      
    public:
      SphereCollisionShape(const glm::vec3& center, float radius) 
        : m_sphere(center, radius) {}
      
      SphereCollisionShape(const CollisionMath::Sphere& sphere) 
        : m_sphere(sphere) {}
      
      std::optional<glm::vec3> checkPointCollision(const glm::vec3& point) const override {
        float distance = glm::length(point - m_sphere.center);
        if (distance <= m_sphere.radius) {
          // Point is inside sphere - calculate collision normal
          if (distance > 0.0f) {
            return glm::normalize(point - m_sphere.center);
          } else {
            // Point is exactly at center - return arbitrary normal
            return glm::vec3(0.0f, 1.0f, 0.0f);
          }
        }
        return std::nullopt;
      }
      
      std::optional<glm::vec3> checkSphereCollision(const CollisionMath::Sphere& sphere) const override {
        float distance = glm::length(sphere.center - m_sphere.center);
        float combinedRadius = sphere.radius + m_sphere.radius;
        
        if (distance <= combinedRadius) {
          // Spheres are intersecting
          if (distance > 0.0f) {
            return glm::normalize(sphere.center - m_sphere.center);
          } else {
            // Spheres have same center - return arbitrary normal
            return glm::vec3(0.0f, 1.0f, 0.0f);
          }
        }
        return std::nullopt;
      }
      
      glm::vec3 getClosestPoint(const glm::vec3& point) const override {
        glm::vec3 direction = point - m_sphere.center;
        float distance = glm::length(direction);
        
        if (distance <= m_sphere.radius) {
          // Point is inside sphere - return the point itself
          return point;
        } else {
          // Point is outside - return closest point on surface
          return m_sphere.center + glm::normalize(direction) * m_sphere.radius;
        }
      }
      
      float getDistanceToSurface(const glm::vec3& point) const override {
        float distance = glm::length(point - m_sphere.center);
        return distance - m_sphere.radius; // Negative if inside
      }
      
      glm::vec3 resolveSphereCollision(const CollisionMath::Sphere& sphere) const override {
        float distance = glm::length(sphere.center - m_sphere.center);
        float combinedRadius = sphere.radius + m_sphere.radius;
        
        if (distance < combinedRadius && distance > 0.0f) {
          // Spheres are intersecting - push apart
          glm::vec3 direction = glm::normalize(sphere.center - m_sphere.center);
          float penetration = combinedRadius - distance;
          return sphere.center + direction * penetration;
        }
        
        return sphere.center;
      }
      
      const CollisionMath::Sphere& getSphere() const { return m_sphere; }
  };
  
} // namespace IronFrost

#endif
