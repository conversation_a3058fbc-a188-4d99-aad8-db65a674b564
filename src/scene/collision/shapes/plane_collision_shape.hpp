#ifndef __IF__PLANE_COLLISION_SHAPE_HPP
#define __IF__PLANE_COLLISION_SHAPE_HPP

// Local includes
#include "collision_shape.hpp"

namespace IronFrost {
  
  /**
   * Plane collision shape - represents an infinite plane
   */
  class PlaneCollisionShape : public CollisionShape {
    private:
      CollisionMath::Plane m_plane;
      
    public:
      PlaneCollisionShape(const glm::vec3& normal, float distance) 
        : m_plane(normal, distance) {}
      
      PlaneCollisionShape(const glm::vec3& point, const glm::vec3& normal) 
        : m_plane(point, normal) {}
      
      std::optional<glm::vec3> checkPointCollision(const glm::vec3& point) const override {
        // Check if point is below the plane (collision occurs)
        if (CollisionMath::isPointBelowPlane(point, m_plane)) {
          return m_plane.normal; // Return collision normal
        }
        return std::nullopt;
      }
      
      std::optional<glm::vec3> checkSphereCollision(const CollisionMath::Sphere& sphere) const override {
        // Check if sphere intersects with the plane
        if (CollisionMath::spherePlaneIntersection(sphere, m_plane)) {
          return m_plane.normal; // Return collision normal
        }
        return std::nullopt;
      }
      
      glm::vec3 getClosestPoint(const glm::vec3& point) const override {
        return CollisionMath::projectPointOntoPlane(point, m_plane);
      }
      
      float getDistanceToSurface(const glm::vec3& point) const override {
        return CollisionMath::distanceToPlane(point, m_plane);
      }
      
      glm::vec3 resolveSphereCollision(const CollisionMath::Sphere& sphere) const override {
        return CollisionMath::resolveSpherePlaneCollision(sphere, m_plane);
      }
      
      const CollisionMath::Plane& getPlane() const { return m_plane; }
  };
  
} // namespace IronFrost

#endif
