#ifndef __IF__AABB_COLLISION_SHAPE_HPP
#define __IF__AABB_COLLISION_SHAPE_HPP

// Local includes
#include "collision_shape.hpp"

namespace IronFrost {
  class AABBCollisionShape : public CollisionShape {
    private:
      CollisionMath::AABB m_aabb;
      
    public:
      AABBCollisionShape(const glm::vec3& min, const glm::vec3& max) 
        : m_aabb(min, max) {}
      
      AABBCollisionShape(const CollisionMath::AABB& aabb) 
        : m_aabb(aabb) {}
      
      std::optional<glm::vec3> checkPointCollision(const glm::vec3& point) const override {
        if (CollisionMath::isPointInAABB(point, m_aabb)) {
          // Point is inside AABB - calculate collision normal
          glm::vec3 center = m_aabb.getCenter();
          glm::vec3 size = m_aabb.getSize();
          glm::vec3 localPoint = point - center;
          
          // Find the axis with the smallest penetration depth
          glm::vec3 absLocal = glm::abs(localPoint);
          glm::vec3 halfSize = size * 0.5f;
          glm::vec3 penetration = halfSize - absLocal;
          
          // Find minimum penetration axis
          if (penetration.x <= penetration.y && penetration.x <= penetration.z) {
            return glm::vec3(localPoint.x > 0 ? 1.0f : -1.0f, 0.0f, 0.0f);
          } else if (penetration.y <= penetration.z) {
            return glm::vec3(0.0f, localPoint.y > 0 ? 1.0f : -1.0f, 0.0f);
          } else {
            return glm::vec3(0.0f, 0.0f, localPoint.z > 0 ? 1.0f : -1.0f);
          }
        }
        return std::nullopt;
      }
      
      std::optional<glm::vec3> checkSphereCollision(const CollisionMath::Sphere& sphere) const override {
        // Check if sphere intersects with the AABB
        if (CollisionMath::sphereAABBIntersection(sphere, m_aabb)) {
          // Calculate collision normal from closest point on AABB to sphere center
          glm::vec3 closestPoint = CollisionMath::closestPointOnAABB(sphere.center, m_aabb);
          glm::vec3 normal = glm::normalize(sphere.center - closestPoint);
          return normal;
        }
        return std::nullopt;
      }
      
      glm::vec3 getClosestPoint(const glm::vec3& point) const override {
        return CollisionMath::closestPointOnAABB(point, m_aabb);
      }
      
      float getDistanceToSurface(const glm::vec3& point) const override {
        if (CollisionMath::isPointInAABB(point, m_aabb)) {
          // Point is inside - return negative distance
          return -CollisionMath::distanceToAABB(point, m_aabb);
        }
        return CollisionMath::distanceToAABB(point, m_aabb);
      }
      
      glm::vec3 resolveSphereCollision(const CollisionMath::Sphere& sphere) const override {
        if (CollisionMath::sphereAABBIntersection(sphere, m_aabb)) {
          // Find closest point on AABB and push sphere away
          glm::vec3 closestPoint = CollisionMath::closestPointOnAABB(sphere.center, m_aabb);
          glm::vec3 direction = sphere.center - closestPoint;
          float distance = glm::length(direction);
          
          if (distance < sphere.radius && distance > 0.0f) {
            glm::vec3 normal = direction / distance;
            float penetration = sphere.radius - distance;
            return sphere.center + normal * penetration;
          }
        }
        return sphere.center;
      }
      
      const CollisionMath::AABB& getAABB() const { return m_aabb; }
  };
  
} // namespace IronFrost

#endif
