#ifndef __IF__COLLISION_SHAPES_HPP
#define __IF__COLLISION_SHAPES_HPP

/**
 * Convenience header that includes all collision-related types
 * Use this when you need access to multiple collision components
 */

// Collision shape interface
#include "collision_shape.hpp"

// Concrete collision shape implementations
#include "plane_collision_shape.hpp"
#include "aabb_collision_shape.hpp"
#include "sphere_collision_shape.hpp"

#endif
