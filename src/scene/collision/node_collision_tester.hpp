#ifndef __IF__NODE_COLLISION_TESTER_HPP
#define __IF__NODE_COLLISION_TESTER_HPP

// C++ standard library
#include <optional>

// Local includes
#include "collision_transform.hpp"
#include "collision_query_result.hpp"
#include "../scene_graph/scene_graph.hpp"

namespace IronFrost {
  
  /**
   * Handles collision testing for individual scene nodes
   * Encapsulates the logic for testing different collision queries against a single node
   */
  class NodeCollisionTester {
    public:
      /**
       * Test point collision against a scene node
       */
      static std::optional<CollisionQueryHit> testPointCollision(
        SceneNode& node,
        const glm::mat4& worldTransform,
        const glm::vec3& worldPoint
      ) {
        if (!hasCollision(node)) {
          return std::nullopt;
        }
        
        CollisionTransform transform(worldTransform);
        glm::vec3 localPoint = transform.worldToLocal(worldPoint);
        
        auto collision = node.sceneCollision->shape->checkPointCollision(localPoint);
        if (collision.has_value()) {
          glm::vec3 worldNormal = transform.transformNormal(collision.value());
          glm::vec3 localClosestPoint = node.sceneCollision->shape->getClosestPoint(localPoint);
          glm::vec3 worldClosestPoint = transform.localToWorld(localClosestPoint);
          float distance = glm::length(worldPoint - worldClosestPoint);

          return CollisionQueryHit(worldNormal, worldClosestPoint, distance);
        }
        
        return std::nullopt;
      }
      
      /**
       * Test sphere collision against a scene node
       */
      static std::optional<CollisionQueryHit> testSphereCollision(
        SceneNode& node,
        const glm::mat4& worldTransform,
        const CollisionMath::Sphere& worldSphere
      ) {
        if (!hasCollision(node)) {
          return std::nullopt;
        }
        
        CollisionTransform transform(worldTransform);
        CollisionMath::Sphere localSphere = transform.worldToLocal(worldSphere);
        
        auto collision = node.sceneCollision->shape->checkSphereCollision(localSphere);
        if (collision.has_value()) {
          glm::vec3 worldNormal = transform.transformNormal(collision.value());
          glm::vec3 localClosestPoint = node.sceneCollision->shape->getClosestPoint(localSphere.center);
          glm::vec3 worldClosestPoint = transform.localToWorld(localClosestPoint);
          float distance = glm::length(worldSphere.center - worldClosestPoint);

          return CollisionQueryHit(worldNormal, worldClosestPoint, distance);
        }
        
        return std::nullopt;
      }
      
      /**
       * Resolve sphere collision against a scene node
       */
      static glm::vec3 resolveSphereCollision(
        SceneNode& node,
        const glm::mat4& worldTransform,
        const CollisionMath::Sphere& worldSphere
      ) {
        if (!hasCollision(node)) {
          return worldSphere.center;
        }
        
        CollisionTransform transform(worldTransform);
        CollisionMath::Sphere localSphere = transform.worldToLocal(worldSphere);
        
        glm::vec3 resolvedLocalCenter = node.sceneCollision->shape->resolveSphereCollision(localSphere);
        return transform.localToWorld(resolvedLocalCenter);
      }
      
    private:
      /**
       * Check if a node has collision data
       */
      static bool hasCollision(const SceneNode& node) {
        return node.sceneCollision.has_value() && node.sceneCollision->shape;
      }
  };
  
} // namespace IronFrost

#endif
