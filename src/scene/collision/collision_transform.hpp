#ifndef __IF__COLLISION_TRANSFORM_HPP
#define __IF__COLLISION_TRANSFORM_HPP

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../../utils/collision_math.hpp"

namespace IronFrost {
  
  /**
   * <PERSON>les coordinate space transformations for collision detection
   * Encapsulates the complex matrix math needed for world<->local space conversion
   */
  class CollisionTransform {
    private:
      glm::mat4 m_worldTransform;
      glm::mat4 m_inverseTransform;
      glm::mat4 m_normalTransform;
      
    public:
      explicit CollisionTransform(const glm::mat4& worldTransform) 
        : m_worldTransform(worldTransform) {
        m_inverseTransform = glm::inverse(worldTransform);
        m_normalTransform = glm::transpose(m_inverseTransform);
      }
      
      /**
       * Transform a point from world space to local space
       */
      glm::vec3 worldToLocal(const glm::vec3& worldPoint) const {
        glm::vec4 localPoint4 = m_inverseTransform * glm::vec4(worldPoint, 1.0f);
        return glm::vec3(localPoint4) / localPoint4.w;
      }
      
      /**
       * Transform a point from local space to world space
       */
      glm::vec3 localToWorld(const glm::vec3& localPoint) const {
        glm::vec4 worldPoint4 = m_worldTransform * glm::vec4(localPoint, 1.0f);
        return glm::vec3(worldPoint4) / worldPoint4.w;
      }
      
      /**
       * Transform a normal from local space to world space
       */
      glm::vec3 transformNormal(const glm::vec3& localNormal) const {
        glm::vec4 worldNormal4 = m_normalTransform * glm::vec4(localNormal, 0.0f);
        return glm::normalize(glm::vec3(worldNormal4));
      }
      
      /**
       * Transform a sphere from world space to local space
       */
      CollisionMath::Sphere worldToLocal(const CollisionMath::Sphere& worldSphere) const {
        glm::vec3 localCenter = worldToLocal(worldSphere.center);
        // Note: For non-uniform scaling, radius transformation would be more complex
        return CollisionMath::Sphere(localCenter, worldSphere.radius);
      }
  };
  
} // namespace IronFrost

#endif
