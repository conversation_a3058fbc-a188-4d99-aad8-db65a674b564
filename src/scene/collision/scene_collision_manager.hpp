#ifndef __IF__SCENE_COLLISION_MANAGER_HPP
#define __IF__SCENE_COLLISION_MANAGER_HPP

// C++ standard library
#include <optional>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "collision_query_result.hpp"
#include "node_collision_tester.hpp"
#include "../scene_graph/scene_graph.hpp"

namespace IronFrost {
  
  /**
   * Scene collision manager with clear separation of concerns
   */
  class SceneCollisionManager {
    private:
      ISceneGraph* m_sceneGraph;
      
    public:
      explicit SceneCollisionManager(ISceneGraph* sceneGraph)
        : m_sceneGraph(sceneGraph) {}
      
      /**
       * Check if a point collides with any objects in the scene
       * Returns the normal of the closest collision, or nullopt if no collision
       */
      std::optional<glm::vec3> checkPointCollision(const glm::vec3& point) const {
        CollisionQueryResult result = queryPointCollisions(point);
        return result.getClosestNormal();
      }
      
      /**
       * Check if a sphere collides with any objects in the scene
       * Returns the normal of the closest collision, or nullopt if no collision
       */
      std::optional<glm::vec3> checkSphereCollision(const glm::vec3& center, float radius) const {
        CollisionQueryResult result = querySphereCollisions(CollisionMath::Sphere(center, radius));
        return result.getClosestNormal();
      }
      
      /**
       * Resolve sphere collision by returning a corrected position
       * Returns the original center if no collision occurs
       */
      glm::vec3 resolveSphereCollision(const glm::vec3& center, float radius) const {
        if (!m_sceneGraph) {
          return center;
        }

        CollisionMath::Sphere sphere(center, radius);
        glm::vec3 resolvedCenter = center;

        m_sceneGraph->traverse([&](SceneNode& node, const glm::mat4& worldTransform) {
          resolvedCenter = NodeCollisionTester::resolveSphereCollision(node, worldTransform,
            CollisionMath::Sphere(resolvedCenter, radius));
        });

        return resolvedCenter;
      }

      /**
       * Get the closest surface point to a given point
       */
      glm::vec3 getClosestSurfacePoint(const glm::vec3& point) const {
        CollisionQueryResult result = queryPointCollisions(point);
        auto closest = result.getClosestHit();
        return closest ? closest->point : point;
      }

      /**
       * Get the number of collision shapes in the scene
       */
      size_t getCollisionShapeCount() const {
        if (!m_sceneGraph) {
          return 0;
        }

        size_t count = 0;
        m_sceneGraph->traverse([&](SceneNode& node, const glm::mat4&) {
          if (node.sceneCollision.has_value() && node.sceneCollision->shape) {
            count++;
          }
        });

        return count;
      }

    private:
      /**
       * Get detailed collision information for a point query
       * Returns all collisions found, not just the closest one
       * Made private since it's only used internally
       */
      CollisionQueryResult queryPointCollisions(const glm::vec3& point) const {
        CollisionQueryResult result;

        if (!m_sceneGraph) {
          return result;
        }

        m_sceneGraph->traverse([&](SceneNode& node, const glm::mat4& worldTransform) {
          auto hit = NodeCollisionTester::testPointCollision(node, worldTransform, point);
          if (hit.has_value()) {
            result.addHit(hit.value());
          }
        });

        return result;
      }

      /**
       * Get detailed collision information for a sphere query
       * Returns all collisions found, not just the closest one
       * Made private since it's only used internally
       */
      CollisionQueryResult querySphereCollisions(const CollisionMath::Sphere& sphere) const {
        CollisionQueryResult result;

        if (!m_sceneGraph) {
          return result;
        }

        m_sceneGraph->traverse([&](SceneNode& node, const glm::mat4& worldTransform) {
          auto hit = NodeCollisionTester::testSphereCollision(node, worldTransform, sphere);
          if (hit.has_value()) {
            result.addHit(hit.value());
          }
        });

        return result;
      }
  };
  
} // namespace IronFrost

#endif
