#ifndef __IF__COLLISION_QUERY_RESULT_HPP
#define __IF__COLLISION_QUERY_RESULT_HPP

// C++ standard library
#include <vector>
#include <optional>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../scene_graph/scene_graph.hpp"

namespace IronFrost {
  
  /**
   * Represents a single collision detection result
   */
  struct CollisionQueryHit {
    glm::vec3 normal;        // Collision normal in world space
    glm::vec3 point;         // Contact point in world space
    float distance;          // Distance to collision

    CollisionQueryHit(const glm::vec3& normal, const glm::vec3& point, float distance)
      : normal(normal), point(point), distance(distance) {}
  };

  /**
   * Collection of collision results with utility methods
   */
  class CollisionQueryResult {
    private:
      std::vector<CollisionQueryHit> m_hits;

    public:
      CollisionQueryResult() = default;

      /**
       * Add a collision hit to the results
       */
      void addHit(const CollisionQueryHit& hit) {
        m_hits.push_back(hit);
      }

      /**
       * Get the closest collision hit
       */
      std::optional<CollisionQueryHit> getClosestHit() const {
        if (m_hits.empty()) {
          return std::nullopt;
        }

        auto closest = std::min_element(m_hits.begin(), m_hits.end(),
          [](const CollisionQueryHit& a, const CollisionQueryHit& b) {
            return a.distance < b.distance;
          });

        return *closest;
      }

      /**
       * Get the normal of the closest collision (convenience method)
       */
      std::optional<glm::vec3> getClosestNormal() const {
        auto closest = getClosestHit();
        return closest ? std::optional<glm::vec3>(closest->normal) : std::nullopt;
      }
  };
  
} // namespace IronFrost

#endif
