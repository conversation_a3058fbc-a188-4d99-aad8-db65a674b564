#ifndef __IF__SCENE_LOADER_HPP
#define __IF__SCENE_LOADER_HPP

// C++ standard library
#include <functional>
#include <memory>
#include <optional>
#include <string>
#include <vector>

// Third-party libraries
#define GLM_ENABLE_EXPERIMENTAL
#define GLM_FORCE_SWIZZLE
#include <glm/glm.hpp>
#include <glm/gtc/quaternion.hpp>
#include <nlohmann/json.hpp>

// Local includes
#include "../scene_graph/scene_entities.hpp"
#include "../scene_lighting.hpp"
#include "../scene_context.hpp"

using json = nlohmann::json;

namespace IronFrost {
  class AssetsManager;
  class IRenderer;
  class IVFS;
  class StringID;
  struct SceneNode;
  class Widget;
  class ImageWidget;
  class LabelWidget;

  class SceneLoader {
    private:
      SceneContext& m_sceneContext;

      json m_sceneConfig;

      SceneNode loadSceneNode(const IRenderer& _renderer, json _node) const;
      
      std::optional<SceneObject> tryLoadObject(const IRenderer& _renderer, json _object) const;
      std::optional<SceneModel> tryLoadModel(const IRenderer& _renderer, const json& _model) const;
      SceneLight loadLight(json _light) const;
      std::optional<SceneCollision> tryLoadCollision(json _collision) const;

      std::unique_ptr<ImageWidget> loadImageWidget(json _widget) const;
      std::unique_ptr<LabelWidget> loadLabelWidget(json _widget) const;

    public:
      SceneLoader(SceneContext& sceneContext, const std::string &sceneConfigPath);

      void loadObjects(const IRenderer& _renderer) const;
      void loadLighting(SceneLighting& _sceneLighting) const;
      void loadScene(const IRenderer& _renderer, const std::function<void(const StringID _name, SceneNode&&)>& _callback) const;
      void loadGUI(const std::function<void(const StringID _name, std::unique_ptr<Widget> _widget)>& _callback) const;

      // Script loading methods - returns script content, not just paths
      std::vector<std::string> getSceneInitScriptContents() const;
      std::vector<std::string> getScenePerFrameScriptContents() const;
  };
}

#endif
