#ifndef __IF__SCENE_ENTITIES_HPP
#define __IF__SCENE_ENTITIES_HPP

// C++ standard library
#include <memory>
#include <variant>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../../renderer/renderables/renderables.hpp"
#include "../collision/shapes/collision_shape.hpp"

namespace IronFrost {
  struct SceneObject {
    RenderableObjectID renderableObjectID;

    glm::vec2 uvTiling{1.0f, 1.0f};
    CollisionMath::AABB bounds{glm::vec3(0.0f), glm::vec3(0.0f)};
  };

  struct SceneModel {
    RenderableModelID renderableModelID;

    CollisionMath::AABB bounds{glm::vec3(0.0f), glm::vec3(0.0f)};
  };

  struct SceneLight {
    glm::vec3 color{1.0f, 1.0f, 1.0f};
    float intensity{1.0f};

    float constant{1.0f};
    float linear{0.09f};
    float quadratic{0.032f};
  };

  struct SceneCollision {
    std::unique_ptr<CollisionShape> shape;

    SceneCollision() = default;
    SceneCollision(std::unique_ptr<CollisionShape> collisionShape)
      : shape(std::move(collisionShape)) {}

    // Move constructor and assignment
    SceneCollision(SceneCollision&& other) noexcept
      : shape(std::move(other.shape)) {}

    SceneCollision& operator=(SceneCollision&& other) noexcept {
      if (this != &other) {
        shape = std::move(other.shape);
      }
      return *this;
    }

    // Delete copy constructor and assignment
    SceneCollision(const SceneCollision&) = delete;
    SceneCollision& operator=(const SceneCollision&) = delete;
  };

  using SceneEntity = std::variant<SceneObject, SceneModel, SceneLight>;
}

#endif
