#ifndef __IF__IMAGE_WIDGET_HPP
#define __IF__IMAGE_WIDGET_HPP

// C++ standard library
#include <string>

// Local includes
#include "../../services/resource_orchestrator/fallback_resources.hpp"
#include "mixins/colorable.hpp"
#include "widget.hpp"

namespace IronFrost {
  class ImageWidget : public Widget, public Colorable {
    private:
      StringID m_texture;

    public:
      ImageWidget() :
        Widget(),
        m_texture(FallbackResources::FALLBACK_TEXTURE_NAME)
      {}

      explicit ImageWidget(const StringID& _texture) :
        Widget(),
        m_texture(_texture)
      {}

      ImageWidget(glm::vec2 _position, glm::vec2 _size, const StringID& _texture) :
        Widget(_position, _size),
        m_texture(_texture)
      {}

      void setTextureName(const StringID& _texture) {
        m_texture = _texture;
        markDirty();
      }

      const StringID& getTextureName() const {
        return m_texture;
      }

      std::string getType() const override {
        return "image";
      }
  };
}

#endif
