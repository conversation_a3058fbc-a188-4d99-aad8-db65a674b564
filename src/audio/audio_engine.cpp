#include "audio_engine.hpp"

// Local includes
#include "../events/event_dispatcher.hpp"
#include "../services/service_locator.hpp"
#include "../services/resource_orchestrator/resource_events.hpp"
#include "../services/resource_orchestrator/resource_type.hpp"
#include "openal/openal_audio_engine.hpp"


namespace IronFrost {
  IAudioEngine::IAudioEngine() {
    EventDispatcher& eventDispatcher = ServiceLocator::getService<EventDispatcher>();

    eventDispatcher.registerListener<LoadAudioEvent>([&](const LoadAudioEvent& event) {
      loadAudioClip(event.name(), event.audioData());
      event.callCallback();
    });
  }

  bool IAudioEngine::isAudioClipLoaded(const StringID& id) const {
    return m_audioClips.find(id) != m_audioClips.end();
  }

  std::unique_ptr<IAudioEngine> IAudioEngine::create(AUDIO_ENGINE_TYPE type) {
    switch (type) {
      case AUDIO_ENGINE_TYPE::OPENAL_ENGINE:
        return std::make_unique<OpenALAudioEngine>();
      default:
        throw std::runtime_error("Unknown audio engine type");
    }
  }

}
