#include "application_builder.hpp"

// C++ standard library
#include <filesystem>
#include <iostream>
#include <memory>

// Local includes
#include "application.hpp"
#include "../assets/assets_manager.hpp"
#include "../audio/audio_engine.hpp"
#include "config/config.hpp"
#include "console_manager.hpp"
#include "../events/event_dispatcher.hpp"
#include "../gui/gui.hpp"
#include "../renderer/renderer.hpp"
#include "../scene/scene_manager.hpp"
#include "../scripts/script_engine.hpp"
#include "../services/resource_orchestrator/resource_orchestrator.hpp"
#include "../services/service_locator.hpp"
#include "system_initializer.hpp"
#include "../utils/game_context.hpp"
#include "../utils/string_id.hpp"
#include "../vfs/vfs.hpp"
#include "../window/console/console.hpp"
#include "../window/window.hpp"

namespace IronFrost {
  ApplicationBuilder& ApplicationBuilder::withWindow(int width, int height, const std::string& title) {
    m_windowWidth = width;
    m_windowHeight = height;
    m_windowTitle = title;
    return *this;
  }

  ApplicationBuilder& ApplicationBuilder::withWindowLibrary(WINDOW_LIBRARY library) {
    m_windowLibrary = library;
    return *this;
  }

  ApplicationBuilder& ApplicationBuilder::withRenderer(RENDERER_TYPE type) {
    m_rendererType = type;
    return *this;
  }

  ApplicationBuilder& ApplicationBuilder::withScriptEngine(SCRIPT_ENGINE_TYPE type) {
    m_scriptEngineType = type;
    return *this;
  }

  ApplicationBuilder& ApplicationBuilder::withoutScripts() {
    m_enableScripts = false;
    return *this;
  }

  ApplicationBuilder& ApplicationBuilder::withScenes(const std::string& configPath, const std::string& defaultScene) {
    m_scenesConfigPath = configPath;
    m_defaultSceneName = defaultScene;
    return *this;
  }

  ApplicationBuilder& ApplicationBuilder::withoutScenes() {
    m_enableScenes = false;
    return *this;
  }

  ApplicationBuilder& ApplicationBuilder::withoutConsole() {
    m_enableConsole = false;
    return *this;
  }

  std::unique_ptr<Application> ApplicationBuilder::build() {
    std::cout << "Building application with ApplicationBuilder" << '\n';

    auto app = std::make_unique<Application>();

    createCoreServices(*app);

    Config config(*app->m_vfs);

    createGameContext(*app);
    createConsole(*app);
    createScriptEngine(*app, config);
    createSceneManager(*app);
    
    config.loadGlobalAssets(*app->m_renderer, *app->m_assetsManager, *app->m_audioEngine);

    std::cout << "  Loading scenes from: " << m_scenesConfigPath << '\n';
    app->m_sceneManager->loadScenesFromFile(m_scenesConfigPath);
    app->m_sceneManager->switchToScene(StringID(m_defaultSceneName));

    SystemInitializer::registerGlobalEventListeners(*app);
    std::cout << "Application built successfully" << '\n';
    return app;
  }

  void ApplicationBuilder::createCoreServices(Application& app) {
    std::cout << "Registering core services" << '\n';

    // Create window
    std::cout << "  Creating window: " << m_windowWidth << "x" << m_windowHeight << " '" << m_windowTitle << "'" << '\n';
    app.m_window = IWindow::create(m_windowLibrary, m_windowWidth, m_windowHeight, m_windowTitle);
    if (!app.m_window) {
      throw std::runtime_error("Failed to create window");
    }

    // Create renderer
    std::cout << "  Creating renderer" << '\n';
    app.m_renderer = IRenderer::create(m_rendererType, *app.m_window);

    // Create VFS
    std::cout << "  Creating VFS" << '\n';
    app.m_vfs = IVFS::create();

    // Create assets manager
    std::cout << "  Creating assets manager" << '\n';
    app.m_assetsManager = std::make_unique<AssetsManager>(*app.m_vfs);

    // Create audio engine
    std::cout << "  Creating audio engine" << '\n';
    app.m_audioEngine = IAudioEngine::create(AUDIO_ENGINE_TYPE::OPENAL_ENGINE);
    if (!app.m_audioEngine->initialize()) {
      throw std::runtime_error("Failed to initialize audio engine");
    }

    // Register ResourceOrchestrator service
    std::cout << "  Registering ResourceOrchestrator service" << '\n';
    EventDispatcher& eventDispatcher = ServiceLocator::getService<EventDispatcher>();
    auto resourceOrchestrator = std::make_unique<ResourceOrchestrator>(*app.m_assetsManager, eventDispatcher);
    ServiceLocator::registerService<ResourceOrchestrator>(std::move(resourceOrchestrator));
  }

  void ApplicationBuilder::createGameContext(Application& app) {
    std::cout << "  Creating game context" << '\n';
    app.m_gameContext = std::make_unique<GameContext>(GameContext{
      .keyboard = app.m_window->getKeyboard(),
      .mouse = app.m_window->getMouse()
    });
  }

  void ApplicationBuilder::createConsole(Application& app) {
    if (!m_enableConsole) return;

    std::cout << "  Creating console" << '\n';
    app.m_console = std::make_unique<Console>();
    app.m_globalGUI = std::make_unique<GUI>();
    app.m_consoleManager = std::make_unique<ConsoleManager>(*app.m_globalGUI, *app.m_console);
    app.m_consoleManager->initialize();
  }

  void ApplicationBuilder::createScriptEngine(Application& app, const Config& config) {
    if (!m_enableScripts) return;

    // Initialize script engine BEFORE scene creation (critical for proper initialization order)
    std::cout << "  Creating script engine" << '\n';
    app.m_scriptEngine = IScriptEngine::create(m_scriptEngineType, app);
    app.m_scriptEngine->registerListeners();

    // Load script content from configuration (Config handles file I/O)
    auto initScriptContents = config.getApplicationInitScriptContents();
    auto perFrameScriptContents = config.getApplicationPerFrameScriptContents();

    // Pass content to script engine
    app.m_scriptEngine->executeInitScripts(initScriptContents);
    app.m_scriptEngine->registerPerFrameScripts(perFrameScriptContents);
  }

  void ApplicationBuilder::createSceneManager(Application& app) {
    if (!m_enableScenes) return;

    // Initialize scene manager AFTER script engine
    std::cout << "  Creating scene manager" << '\n';
    app.m_sceneManager = std::make_unique<SceneManager>(*app.m_vfs, *app.m_assetsManager, *app.m_renderer, app.m_scriptEngine.get());
  }
}
