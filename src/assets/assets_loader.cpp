#include "assets_loader.hpp"

// C++ standard library
#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

// Local includes
#include "../utils/string_id.hpp"
#include "../vfs/vfs.hpp"
#include "assets_manager.hpp"
#include "loaders/image_loader.hpp"
#include "processors/image_atlas_processor.hpp"
#include "writers/image_writer.hpp"
#include "generators/default_texture_generator.hpp"

namespace IronFrost {

  const std::unordered_map<std::string, PrimitiveType> AssetsLoader::primitiveTypes = {
    {"triangle", PrimitiveType::TRIANGLE},
    {"quad", PrimitiveType::QUAD},
    {"cube", PrimitiveType::CUBE},
    {"cone", PrimitiveType::CONE},
    {"cylinder", PrimitiveType::CYLINDER},
    {"sphere", PrimitiveType::SPHERE},
    {"plane", PrimitiveType::PLANE}
  };
  
  std::unique_ptr<AssetsLoader> AssetsLoader::fromFile(IVFS& vfs, const std::string& path) {
    return std::make_unique<AssetsFromFileLoader>(vfs, path);
  }

  BlinnPhongMaterialData AssetsFromFileLoader::loadBlinnPhongMaterial(AssetsManager& assetsManager, const json& material) const {
    BlinnPhongMaterialData blinnPhongMaterialData;

    blinnPhongMaterialData.ambient = material.contains("ambient") 
      ? glm::vec3{ material["ambient"][0], material["ambient"][1], material["ambient"][2] } 
      : glm::vec3(1.0F);

    blinnPhongMaterialData.diffuse = material.contains("diffuse")
      ? glm::vec3{ material["diffuse"][0], material["diffuse"][1], material["diffuse"][2] }
      : glm::vec3(1.0F);

    blinnPhongMaterialData.specular = material.contains("specular")
      ? glm::vec3{ material["specular"][0], material["specular"][1], material["specular"][2] }
      : glm::vec3(1.0F);

    blinnPhongMaterialData.shininess = material.contains("shininess")
      ? material["shininess"].get<float>()
      : 32.0F;

    blinnPhongMaterialData.textureName = StringID(material.contains("texture") ? material["texture"] : "");

    return blinnPhongMaterialData;
  }

  PBRMaterialData AssetsFromFileLoader::loadPBRMaterial(AssetsManager& assetsManager, const json& material) const {
    // BaseColor is required for PBR materials
    if (!material.contains("baseColor")) {
      throw std::runtime_error("PBR material must have a baseColor texture");
    }

    PBRMaterialData pbrMaterialData;
    pbrMaterialData.textureArray.reserve(6);

    // Load baseColor texture to determine reference size
    auto baseColorTexture = assetsManager.loadImage(material["baseColor"]);
    int width = baseColorTexture->width;
    int height = baseColorTexture->height;

    // Define texture types and their default generators
    struct TextureInfo {
      const char* key;
      std::function<std::unique_ptr<ImageData>(int, int)> defaultGenerator;
    };

    std::vector<TextureInfo> textureTypes = {
      {"baseColor", [](int w, int h) { return DefaultTextureGenerator::createBaseColorTexture(w, h); }},
      {"normal", [](int w, int h) { return DefaultTextureGenerator::createNormalTexture(w, h); }},
      {"metallic", [](int w, int h) { return DefaultTextureGenerator::createMetallicTexture(w, h); }},
      {"roughness", [](int w, int h) { return DefaultTextureGenerator::createRoughnessTexture(w, h); }},
      {"ao", [](int w, int h) { return DefaultTextureGenerator::createAOTexture(w, h); }},
      {"emissive", [](int w, int h) { return DefaultTextureGenerator::createEmissiveTexture(w, h); }}
    };

    // Add baseColor texture first
    pbrMaterialData.textureArray.emplace_back(std::move(baseColorTexture));

    // Load or generate remaining textures
    for (size_t i = 1; i < textureTypes.size(); i++) {
      const auto& textureType = textureTypes[i];

      if (material.contains(textureType.key)) {
        pbrMaterialData.textureArray.emplace_back(assetsManager.loadImage(material[textureType.key]));
      } else {
        pbrMaterialData.textureArray.emplace_back(textureType.defaultGenerator(width, height));
      }
    }

    return pbrMaterialData;
  }

  void AssetsFromFileLoader::processElement(const std::string& element, std::function<void(const json& element)> callback) const {
    if(!m_assetsConfig.contains(element)) {
      return;
    }

    auto elementConfig = m_assetsConfig[element];

    for (json::const_iterator it = elementConfig.begin(); it != elementConfig.end(); ++it) {
      callback(it.value());
    }
  }

  AssetsFromFileLoader::AssetsFromFileLoader(IVFS &vfs, const std::string &path) :
    m_vfs(vfs)
  {
    m_assetsConfig = json::parse(m_vfs.readFile(path));
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<MeshData> callback) const {
    return processElement("meshes", [&](const json& mesh) {
      StringID name = StringID(mesh["name"]);
      std::string type = mesh["type"];

      if(type == "primitive") {
        std::string primitive = mesh["primitive"];
        auto params = PrimitiveParams::fromJSON(mesh);
        callback(name, assetsManager.createPrimitive(name, primitiveTypes.at(primitive), params));
      }
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<ModelData> callback) const {
    return processElement("models", [&](const json& model) {
      StringID name = StringID(model["name"]);
      std::string path = model["path"];

      callback(name, assetsManager.loadModel(name, path));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<ShaderData> callback) const {
    return processElement("shaders", [&](const json& shader) {
      StringID name = StringID(shader["name"]);
      std::string path = shader["path"];

      callback(name, assetsManager.loadShader(name, path));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<ImageData> callback) const {
    return processElement("textures", [&](const json& texture) {
      StringID name = StringID(texture["name"]);
      std::string path = texture["path"];

      callback(name, assetsManager.loadImage(name, path));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<AudioData> callback) const {
    return processElement("audio", [&](const json& audio) {
      StringID name = StringID(audio["name"]);
      std::string path = audio["path"];

      callback(name, assetsManager.loadAudio(name, path));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<FontData> callback) const {
    return processElement("fonts", [&](const json& font) {
      StringID name = StringID(font["name"]);
      std::string path = font["path"];
      int size = font["size"];

      callback(name, assetsManager.loadFont(name, path, size));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<MaterialData> callback) const {
    return processElement("materials", [&](const json& material) {
      StringID name = StringID(material["name"]);
      std::string type = material["type"];

      if (type == "phong") {
        callback(name, MaterialData{loadBlinnPhongMaterial(assetsManager, material)});
      } else if (type == "pbr") {
        callback(name, MaterialData{loadPBRMaterial(assetsManager, material)});
      }
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<PostprocessData> callback) const {
    return processElement("postprocess", [&](const json& postprocess) {
      StringID name = StringID(postprocess["name"]);
      std::vector<std::string> shaderNames = postprocess["shaders"].get<std::vector<std::string>>();

      callback(name, PostprocessData{shaderNames});
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<HeightmapData> callback) const {
    return processElement("heightmaps", [&](const json& heightmap) {
      StringID name = StringID(heightmap["name"]);
      std::string path = heightmap["path"];
      float scale = heightmap["scale"];

      callback(name, assetsManager.loadHeightmap(name, path, scale));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<TerrainData> callback) const {
    return processElement("terrains", [&](const json& terrain) {
      StringID name = StringID(terrain["name"]);
      std::string heightmapName = terrain["heightmap"];

      callback(name, assetsManager.loadTerrain(name, heightmapName));
    });
  }
}
