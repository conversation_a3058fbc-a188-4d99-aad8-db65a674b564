#ifndef __IF__ASSETS_MANIFEST_HPP
#define __IF__ASSETS_MANIFEST_HPP

// C++ standard library
#include <string>
#include <unordered_map>
#include <variant>
#include <vector>

// Third-party libraries
#include <nlohmann/json.hpp>

// Local includes
#include "../utils/string_id.hpp"
#include "../vfs/vfs.hpp"


using json = nlohmann::json;

namespace IronFrost {
  enum class AssetType {
    MESH,
    MODEL,
    SHADER,
    TEXTURE,
    FONT,
    MATERIAL_PHONG,
    MATERIAL_PBR,
    UNKNOWN
  };

  struct AssetInfo {
    StringID name;
    std::string path{""};

    AssetType type{AssetType::UNKNOWN};

    using ConfigValue = std::variant<int, float, bool, std::string, std::vector<std::string>, glm::vec3>;

    std::unordered_map<std::string, ConfigValue> options{};
  };

  class AssetsManifest {
    private:
      std::vector<AssetInfo> assets;

      void loadMeshesInfo(const json& meshesConfig) {
        for (json::const_iterator it = meshesConfig.begin(); it != meshesConfig.end(); ++it) {
          auto mesh = it.value();

          AssetInfo assetInfo;
          assetInfo.name = StringID(mesh["name"]);
          assetInfo.type = AssetType::MESH;
          assetInfo.options["type"] = mesh["type"].get<std::string>(); 
        
          if(mesh["type"] == "primitive") {
            assetInfo.options["primitive"] = mesh["primitive"].get<std::string>();
          }

          assets.push_back(assetInfo);
        }
      }

      void loadModelsInfo(const json& modelsConfig) {
        for (json::const_iterator it = modelsConfig.begin(); it != modelsConfig.end(); ++it) {
          auto model = it.value();

          AssetInfo assetInfo;
          assetInfo.name = StringID(model["name"]);
          assetInfo.path = model["path"];
          assetInfo.type = AssetType::MODEL;
    
          assets.push_back(assetInfo);
        }
      }

      void loadShadersInfo(const json& shadersConfig) {
        for (json::const_iterator it = shadersConfig.begin(); it != shadersConfig.end(); ++it) {
          auto shader = it.value();
    
          AssetInfo assetInfo;
          assetInfo.name = StringID(shader["name"]);
          assetInfo.path = shader["path"];  
          assetInfo.type = AssetType::SHADER;

          assets.push_back(assetInfo);
        }
      }

      void loadTexturesInfo(const json& texturesConfig) {
        for (json::const_iterator it = texturesConfig.begin(); it != texturesConfig.end(); ++it) {
          auto texture = it.value();

          AssetInfo assetInfo;
          assetInfo.name = StringID(texture["name"]);
          assetInfo.path = texture["path"];
          assetInfo.type = AssetType::TEXTURE;

          assets.push_back(assetInfo);
        }
      }

      void loadFontsInfo(const json& fontsConfig) {
        for (json::const_iterator it = fontsConfig.begin(); it != fontsConfig.end(); ++it) {
          auto font = it.value();

          AssetInfo assetInfo;
          assetInfo.name = StringID(font["name"]);
          assetInfo.path = font["path"];
          assetInfo.type = AssetType::FONT;
          assetInfo.options["size"] = font["size"].get<int>();

          assets.push_back(assetInfo);
        }
      }

      void loadMaterialsInfo(const json& materialsConfig) {
        for (json::const_iterator it = materialsConfig.begin(); it != materialsConfig.end(); ++it) {
          auto material = it.value();

          AssetInfo assetInfo;
          assetInfo.name = StringID(material["name"]);

          if (material["type"] == "pbr") {
            assetInfo.type = AssetType::MATERIAL_PBR;
            loadPBRMaterialInfo(material, assetInfo);
          } else if (material["type"] == "phong") {
            assetInfo.type = AssetType::MATERIAL_PHONG;
            loadPhongMaterialInfo(material, assetInfo);
          }

          assets.push_back(assetInfo);
        }
      }

      void loadPhongMaterialInfo(const json& material, AssetInfo& assetInfo) {
        assetInfo.options["ambient"] = material.contains("ambient") 
          ? glm::vec3{ material["ambient"][0], material["ambient"][1], material["ambient"][2] } 
          : glm::vec3(1.0f);

        assetInfo.options["diffuse"] = material.contains("diffuse")
          ? glm::vec3{ material["diffuse"][0], material["diffuse"][1], material["diffuse"][2] }
          : glm::vec3(1.0f);

        assetInfo.options["specular"] = material.contains("specular")
          ? glm::vec3{ material["specular"][0], material["specular"][1], material["specular"][2] }
          : glm::vec3(1.0f);

        assetInfo.options["shininess"] = material.contains("shininess")
          ? material["shininess"].get<float>()
          : 32.0f;

        assetInfo.options["texture"] = material.contains("texture")
          ? material["texture"].get<std::string>()
          : "";
      }

      void loadPBRMaterialInfo(const json& material, AssetInfo& assetInfo) {
        if(material.contains("baseColor")) assetInfo.options["baseColor"] = material["baseColor"].get<std::string>();
        if(material.contains("normal"))    assetInfo.options["normal"]    = material["normal"].get<std::string>();
        if(material.contains("metallic"))  assetInfo.options["metallic"]  = material["metallic"].get<std::string>();
        if(material.contains("roughness")) assetInfo.options["roughness"] = material["roughness"].get<std::string>();
        if(material.contains("ao"))        assetInfo.options["ao"]        = material["ao"].get<std::string>();
        if(material.contains("emissive"))  assetInfo.options["emissive"]  = material["emissive"].get<std::string>();
      }

      void loadPostprocessEffectsInfo(const json& postprocessEffectsConfig) {
        for (json::const_iterator it = postprocessEffectsConfig.begin(); it != postprocessEffectsConfig.end(); ++it) {
          auto postprocessEffect = it.value();
    
          AssetInfo assetInfo;
          assetInfo.name = StringID(postprocessEffect["name"]);

          std::vector<std::string> shaderNames = postprocessEffect["shaders"].get<std::vector<std::string>>();
          assetInfo.options["shaders"] = shaderNames;
    
          assets.push_back(assetInfo);
        }
      }
      
    public:

      static AssetsManifest fromFile(IVFS& vfs, const std::string& path) {
        json assetsConfig = json::parse(vfs.readFile(path));

        AssetsManifest manifest;

        if(assetsConfig.contains("meshes")) manifest.loadMeshesInfo(assetsConfig["meshes"]);
        if(assetsConfig.contains("models")) manifest.loadModelsInfo(assetsConfig["models"]);
        if(assetsConfig.contains("shaders")) manifest.loadShadersInfo(assetsConfig["shaders"]);
        if(assetsConfig.contains("textures")) manifest.loadTexturesInfo(assetsConfig["textures"]);
        if(assetsConfig.contains("fonts")) manifest.loadFontsInfo(assetsConfig["fonts"]);
        if(assetsConfig.contains("materials")) manifest.loadMaterialsInfo(assetsConfig["materials"]);
        if(assetsConfig.contains("postprocess")) manifest.loadPostprocessEffectsInfo(assetsConfig["postprocess"]);

        return manifest;
      }

      auto begin() { return assets.begin(); }
      auto end() { return assets.end(); }
      auto cbegin() const { return assets.cbegin(); }
      auto cend() const { return assets.cend(); }

      size_t size() const { return assets.size(); }
  };
}

#endif
