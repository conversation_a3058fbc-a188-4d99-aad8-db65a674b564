#include "heightmap_loader.hpp"

// C++ standard library
#include <iostream>

// Local includes
#include "../data_types/heightmap_data.hpp"
#include "../data_types/image_data.hpp"
#include "image_loader.hpp"

namespace IronFrost {
  std::unique_ptr<ImageData> HeightmapLoader::loadImage(const std::string& path) {
    ImageLoader imageLoader(m_vfs);
    return imageLoader.loadImage(path, 1);
  }

  HeightmapLoader::HeightmapLoader(IVFS& vfs) : m_vfs{vfs}
  {}

  std::unique_ptr<HeightmapData> HeightmapLoader::loadHeightmap(const std::string& path, float heightScale) {
    auto imageData = loadImage(path);

    if (!imageData || imageData->width == 0 || imageData->height == 0 || !imageData->data) {
      std::cerr << "Failed to load heightmap image data for: " << path << '\n';
      return nullptr;
    }

    auto heightmapData = std::make_unique<HeightmapData>();
    heightmapData->width = imageData->width;
    heightmapData->height = imageData->height;
    heightmapData->scale = heightScale;
    heightmapData->heightmap.resize(imageData->width * imageData->height);

    if (imageData->is16bit) {
      processHeightmapData(imageData->get16BitData(), heightmapData->heightmap, heightScale);
    } else {
      processHeightmapData(imageData->get8BitData(), heightmapData->heightmap, heightScale);
    }

    return heightmapData;
  }
}
