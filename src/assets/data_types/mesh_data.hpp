#ifndef __IF__ASSET_DATA_TYPE_MESH_HPP
#define __IF__ASSET_DATA_TYPE_MESH_HPP

// C++ standard library
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "vertex.hpp"
#include "../../utils/collision_math.hpp"

namespace IronFrost {
  struct MeshData {
    MeshData(std::vector<Vertex> vertices, std::vector<unsigned int> indices)
      : vertices(vertices), indices(indices) {}

    MeshData() = default;

    // Copy semantics are deleted
    MeshData(const MeshData&) = delete;
    MeshData& operator=(const MeshData&) = delete;

    // Move semantics are enabled
    MeshData(MeshData&&) = default;
    MeshData& operator=(MeshData&&) = default;
    
    std::vector<Vertex> vertices{};
    std::vector<unsigned int> indices{};

    CollisionMath::AABB bounds{glm::vec3(0.0f), glm::vec3(0.0f)};

    void calculateBounds() {
      glm::vec3 minPos = vertices[0].getPosition();
      glm::vec3 maxPos = vertices[0].getPosition();

      for (const auto& vertex : vertices) {
        glm::vec3 pos = vertex.getPosition();
        minPos = glm::min(minPos, pos);
        maxPos = glm::max(maxPos, pos);
      }

      bounds = CollisionMath::AABB{minPos, maxPos};
    }

    void calculateNormals() {
      for (auto& vertex : vertices) {
        vertex.setNormal(glm::vec3(0.0f));
      }

      for (size_t i = 0; i < indices.size(); i += 3) {
        unsigned int i0 = indices[i];
        unsigned int i1 = indices[i + 1];
        unsigned int i2 = indices[i + 2];

        glm::vec3 v0 = vertices[i0].getPosition();
        glm::vec3 v1 = vertices[i1].getPosition();
        glm::vec3 v2 = vertices[i2].getPosition();

        glm::vec3 edge1 = v1 - v0;
        glm::vec3 edge2 = v2 - v0;
        glm::vec3 faceNormal = glm::normalize(glm::cross(edge1, edge2));

        vertices[i0].setNormal(vertices[i0].getNormal() + faceNormal);
        vertices[i1].setNormal(vertices[i1].getNormal() + faceNormal);
        vertices[i2].setNormal(vertices[i2].getNormal() + faceNormal);
      }

      for (auto& vertex : vertices) {
        glm::vec3 normal = vertex.getNormal();
        float length = glm::length(normal);
        if (length > 0.0f) {
          vertex.setNormal(normal / length);
        } else {
          vertex.setNormal(glm::vec3(0.0f, 0.0f, 0.0f));
        }
      }
    }

    void calculateTangentsAndBitangents() {
      for (auto& vertex : vertices) {
        vertex.setTangent(glm::vec3(0.0f));
        vertex.setBitangent(glm::vec3(0.0f));
      }

      for (size_t i = 0; i < indices.size(); i += 3) {
        unsigned int i0 = indices[i]; 
        unsigned int i1 = indices[i + 1];
        unsigned int i2 = indices[i + 2];

        const glm::vec3& v0 = vertices[i0].getPosition();
        const glm::vec3& v1 = vertices[i1].getPosition();
        const glm::vec3& v2 = vertices[i2].getPosition();

        const glm::vec2& uv0 = vertices[i0].getUV();
        const glm::vec2& uv1 = vertices[i1].getUV();
        const glm::vec2& uv2 = vertices[i2].getUV();

        glm::vec3 edge1 = v1 - v0;
        glm::vec3 edge2 = v2 - v0;

        glm::vec2 deltaUV1 = uv1 - uv0;
        glm::vec2 deltaUV2 = uv2 - uv0;

        float det = deltaUV1.x * deltaUV2.y - deltaUV1.y * deltaUV2.x;
        if (std::abs(det) < 1e-6f) {
          continue;
        }

        float f = 1.0f / det;

        glm::vec3 tangent{
          f * (deltaUV2.y * edge1.x - deltaUV1.y * edge2.x),
          f * (deltaUV2.y * edge1.y - deltaUV1.y * edge2.y),
          f * (deltaUV2.y * edge1.z - deltaUV1.y * edge2.z)
        };

        glm::vec3 bitangent{
          f * (-deltaUV2.x * edge1.x + deltaUV1.x * edge2.x),
          f * (-deltaUV2.x * edge1.y + deltaUV1.x * edge2.y),
          f * (-deltaUV2.x * edge1.z + deltaUV1.x * edge2.z)
        };

        vertices[i0].setTangent(vertices[i0].getTangent() + tangent);
        vertices[i1].setTangent(vertices[i1].getTangent() + tangent);
        vertices[i2].setTangent(vertices[i2].getTangent() + tangent);

        vertices[i0].setBitangent(vertices[i0].getBitangent() + bitangent);
        vertices[i1].setBitangent(vertices[i1].getBitangent() + bitangent);
        vertices[i2].setBitangent(vertices[i2].getBitangent() + bitangent);
      }

      // Normalize and orthogonalize
      for (auto& vertex : vertices) {
        glm::vec3 n = vertex.getNormal();
        glm::vec3 t = vertex.getTangent();

        // Gram-Schmidt orthogonalize
        t = glm::normalize(t - n * glm::dot(n, t));
        vertex.setTangent(t);

        // Regenerate bitangent to ensure TBN consistency
        glm::vec3 b = glm::normalize(glm::cross(n, t));
        vertex.setBitangent(b);
      }
    }
  };
}

#endif
