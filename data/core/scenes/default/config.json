{"scripts": {"init": ["scripts/scenes/default/init.lua"], "perFrame": ["scripts/scenes/default/update.lua"]}, "lighting": [{"type": "ambient", "color": [1.0, 1.0, 1.0], "intensity": 0.3}, {"type": "directional", "color": [0.1, 0.2, 0.7], "intensity": 1.0, "direction": [-1.0, -1.0, -1.0]}, {"type": "directional", "color": [0.1, 0.7, 0.3], "intensity": 1.0, "direction": [1.0, -1.0, -1.0]}], "objects": [{"name": "wood-sphere", "mesh": "mesh::sphere", "material": "material::pbr::hammered-gold", "shader": "shader::pbr"}, {"name": "ground-plane", "mesh": "mesh::plane", "material": "material::pbr::tiles", "shader": "shader::pbr"}, {"name": "ground-terrain", "mesh": "terrain::ground", "material": "material::pbr::tiles", "shader": "shader::pbr"}, {"name": "wall-sphere", "mesh": "mesh::sphere", "material": "material::wall", "shader": "shader::blinn-phong"}, {"name": "wall-cube", "mesh": "mesh::cube", "material": "material::wall", "shader": "shader::blinn-phong"}, {"name": "light-sphere", "mesh": "mesh::sphere", "material": "material::wood", "shader": "shader::debug"}, {"name": "wall-cylinder", "mesh": "mesh::cylinder", "material": "material::wall", "shader": "shader::blinn-phong"}, {"name": "wall-cone", "mesh": "mesh::cone", "material": "material::wall", "shader": "shader::blinn-phong"}], "scene": [{"name": "sphere", "position": [0.0, 0.0, -2.0], "rotation": [60.0, 0.0, 15.0], "scale": [1.0, 1.0, 1.0], "object": {"name": "wood-sphere", "tiling": [4.0, 4.0]}, "children": [{"name": "sphere_internal", "position": [0.0, 0.0, -1.0], "rotation": [60.0, 0.0, 15.0], "scale": [0.5, 0.5, 0.5], "object": {"name": "wall-sphere", "tiling": [4.0, 4.0]}, "children": [{"name": "sphere_internal_2", "position": [0.0, 0.0, -1.0], "rotation": [60.0, 0.0, 15.0], "scale": [0.25, 0.25, 0.25], "object": {"name": "wall-sphere"}}]}]}, {"name": "cube", "position": [0.0, 0.0, -2.0], "rotation": [60.0, 0.0, 15.0], "scale": [0.75, 0.75, 0.75], "object": {"name": "wall-cube"}}, {"name": "cone", "position": [0.0, 0.5, -4.0], "rotation": [30.0, 0.0, 15.0], "scale": [0.75, 0.75, 0.75], "object": {"name": "wall-cone"}}, {"name": "cylinder", "position": [0.0, 1.0, -6.0], "rotation": [60.0, 0.0, 15.0], "scale": [0.75, 0.75, 0.75], "object": {"name": "wall-cylinder"}}, {"name": "light", "position": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0], "scale": [0.1, 0.1, 0.1], "light": {"color": [1.0, 0.0, 0.0], "intensity": 1.0}, "object": {"name": "light-sphere"}}, {"name": "table", "position": [1.0, 0.0, 1.0], "rotation": [0.0, 0.0, 0.0], "scale": [1.0, 1.0, 1.0], "model": "model::table"}, {"name": "ground", "position": [0.0, -5.0, 0.0], "rotation": [0.0, 0.0, 0.0], "scale": [100.0, 3.0, 100.0], "object": {"name": "ground-terrain", "tiling": [100.0, 100.0]}, "collision": {"type": "plane", "point": [0.0, 0.0, 0.0], "normal": [0.0, 1.0, 0.0]}}], "gui": [{"name": "image", "type": "image", "position": [160.0, 90.0], "size": [500.0, 500.0], "texture": "texture::wood", "color": [1.0, 1.0, 1.0, 1.0]}, {"name": "label", "type": "label", "position": [0.0, 0.0], "size": [1.0, 1.0], "font": "font::console-72", "label": "Test gypqwe!\nNext line\nAnd one more", "color": [1.0, 0.5, 0.5, 1.0]}]}