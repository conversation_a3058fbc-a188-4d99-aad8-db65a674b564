-- Camera Controls Module
-- <PERSON>les mouse look and keyboard camera movement

local CameraControls = {}

-- Initialize camera controls state
function CameraControls.init()
  CameraControls.mouseLook = {
    sensitivity = 0.1,  -- Mouse sensitivity multiplier
    enabled = false,    -- Whether mouse look is currently active
    lastRightButton = false  -- Track right button state for edge detection
  }
end

-- Update camera controls
function CameraControls.update(scene, deltaTime, window)
  if not scene or not window or CONSOLE.isOpen() then
    return
  end

  local mouse = window:getMouse()
  local keyboard = window:getKeyboard()
  local camera = scene:getCamera()

  -- Get collision manager for collision detection
  local collisionManager = nil
  local success, result = pcall(function() return scene:getCollisionManager() end)
  if success then
    collisionManager = result
  end

  -- Handle mouse look
  CameraControls.handleMouseLook(mouse, camera)

  -- Handle keyboard movement
  CameraControls.handleKeyboardMovement(keyboard, camera, deltaTime, collisionManager)

  -- Handle keyboard rotation (only when mouse look is disabled)
  if not CameraControls.mouseLook.enabled then
    CameraControls.handleKeyboardRotation(keyboard, camera, deltaTime)
  end
end

-- Handle mouse look functionality
function CameraControls.handleMouseLook(mouse, camera)
  local rightButtonPressed = mouse:isButtonPressed(MouseButtonType.RIGHT)

  -- Detect right button press (edge detection)
  if rightButtonPressed and not CameraControls.mouseLook.lastRightButton then
    -- Right button just pressed - enable mouse look
    mouse:hideCursor()
    CameraControls.mouseLook.enabled = true
    mouse:resetRelativePosition()  -- Reset to avoid sudden jumps
  elseif not rightButtonPressed and CameraControls.mouseLook.lastRightButton then
    -- Right button just released - disable mouse look
    mouse:showCursor()
    CameraControls.mouseLook.enabled = false
  end
  
  CameraControls.mouseLook.lastRightButton = rightButtonPressed
  
  -- Apply mouse look when enabled
  if CameraControls.mouseLook.enabled then
    local relX = mouse:getRelativeX()
    local relY = mouse:getRelativeY()
    
    -- Only update camera if there's actual mouse movement
    if relX ~= 0.0 or relY ~= 0.0 then
      -- Apply mouse sensitivity and convert to radians
      local yawAngle = relX * CameraControls.mouseLook.sensitivity * math.pi / 180.0
      local pitchAngle = relY * CameraControls.mouseLook.sensitivity * math.pi / 180.0
      
      -- Use mouse look methods that maintain world up vector
      if yawAngle ~= 0.0 then
        camera:mouseLookYaw(-yawAngle)  -- Negative for natural mouse movement
      end
      
      if pitchAngle ~= 0.0 then
        camera:mouseLookPitch(-pitchAngle)  -- Negative for natural mouse movement
      end
      
      -- Reset relative position after processing
      mouse:resetRelativePosition()
    end
  end
end

-- Handle keyboard movement with collision detection
function CameraControls.handleKeyboardMovement(keyboard, camera, deltaTime, collisionManager)
  local moveSpeed = 10.0

  if keyboard:isKeyDown(KeyType.W) then
    if collisionManager then
      local newPosition = camera:calculateForwardMovement(moveSpeed, deltaTime)
      local radius = camera:getCollisionRadius()
      local resolvedPosition = collisionManager:resolveSphereCollision(newPosition, radius)
      camera:setPositionIfValid(resolvedPosition)
    else
      camera:moveForward(moveSpeed, deltaTime)
    end
  end

  if keyboard:isKeyDown(KeyType.S) then
    if collisionManager then
      local newPosition = camera:calculateBackwardMovement(moveSpeed, deltaTime)
      local radius = camera:getCollisionRadius()
      local resolvedPosition = collisionManager:resolveSphereCollision(newPosition, radius)
      camera:setPositionIfValid(resolvedPosition)
    else
      camera:moveBackward(moveSpeed, deltaTime)
    end
  end

  if keyboard:isKeyDown(KeyType.A) then
    if collisionManager then
      local newPosition = camera:calculateLeftMovement(moveSpeed, deltaTime)
      local radius = camera:getCollisionRadius()
      local resolvedPosition = collisionManager:resolveSphereCollision(newPosition, radius)
      camera:setPositionIfValid(resolvedPosition)
    else
      camera:moveLeft(moveSpeed, deltaTime)
    end
  end

  if keyboard:isKeyDown(KeyType.D) then
    if collisionManager then
      local newPosition = camera:calculateRightMovement(moveSpeed, deltaTime)
      local radius = camera:getCollisionRadius()
      local resolvedPosition = collisionManager:resolveSphereCollision(newPosition, radius)
      camera:setPositionIfValid(resolvedPosition)
    else
      camera:moveRight(moveSpeed, deltaTime)
    end
  end
end

-- Handle keyboard rotation
function CameraControls.handleKeyboardRotation(keyboard, camera, deltaTime)
  if keyboard:isKeyDown(KeyType.Q) then
    camera:turnLeft(40.0, deltaTime)
  end

  if keyboard:isKeyDown(KeyType.E) then
    camera:turnRight(40.0, deltaTime)
  end

  if keyboard:isKeyDown(KeyType.R) then
    camera:turnUp(40.0, deltaTime)
  end

  if keyboard:isKeyDown(KeyType.F) then
    camera:turnDown(40.0, deltaTime)
  end
end

return CameraControls
