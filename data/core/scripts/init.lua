-- Test Audio API
print("Testing Audio API...")
print("Is audio::music loaded?", Audio.isAudioClipLoaded("audio::music"))
print("Current music volume:", Audio.getMusicVolume())
print("Is music playing?", Audio.isMusicPlaying())

-- Play music with looping after 3 seconds
setTimeout(function()
  print("Playing music with looping...")
  Audio.playMusic("audio::music", true)
  Audio.setMusicVolume(0.5)
  print("Music started! Volume set to 0.5")
end, 3000)

-- Pause music after 8 seconds
-- setTimeout(function()
--   print("Pausing music...")
--   Audio.pauseMusic()
--   print("Music paused!")
-- end, 8000)

-- Resume music after 10 seconds
-- setTimeout(function()
--   print("Resuming music...")
--   Audio.resumeMusic()
--   print("Music resumed!")
-- end, 10000)

-- Stop music after 15 seconds
-- setTimeout(function()
--   print("Stopping music...")
--   Audio.stopMusic()
--   print("Music stopped!")
-- end, 15000)
