#version 330 core
out vec4 FragColor;

in vec2 TexCoord;
in vec3 Normal;
in vec3 FragPos;

struct BlinnPhongMaterial {
    vec3 ambient;
    vec3 diffuse;
    vec3 specular;
    float shininess;
    sampler2D baseColor;
};
uniform BlinnPhongMaterial phongMaterial;

struct AmbientLight {
    vec3 color;
    float intensity;
};
uniform AmbientLight ambientLight;

struct DirectionalLight {
    vec3 direction;
    vec3 color;
    float intensity;
};

#define MAX_DIRECTIONAL_LIGHTS 4
uniform int numDirectionalLights;
uniform DirectionalLight dirLights[MAX_DIRECTIONAL_LIGHTS];

struct PointLight {
    vec3 position;
    vec3 color;
    float intensity;
    float constant;
    float linear;
    float quadratic;
};

#define MAX_POINT_LIGHTS 8
uniform int numPointLights;
uniform PointLight pointLights[MAX_POINT_LIGHTS];

uniform vec3 cameraPosition;
uniform vec2 uvTiling;

vec3 calculateAmbientLight();
vec3 calculateDirectionalLights(vec3 normal, vec3 viewDir);
vec3 calculatePointLights(vec3 normal, vec3 viewDir);
float calculateAttenuation(PointLight light, float distance);
vec3 calculateBlinnPhong(vec3 normal, vec3 lightDir, vec3 viewDir, vec3 lightColor, float lightIntensity);

void main() {
    vec3 normal = normalize(Normal);
    vec2 tiledUV = clamp(fract(TexCoord * uvTiling), 0.0, 1.0);

    vec3 totalAmbientLight = calculateAmbientLight();
    vec3 totalDirectionalLights = calculateDirectionalLights(normal, normalize(-FragPos));
    vec3 totalPointLights = calculatePointLights(normal, normalize(cameraPosition - FragPos));

    // Combine lighting
    vec3 lighting = totalAmbientLight + totalDirectionalLights + totalPointLights;

    vec3 textureColor = texture(phongMaterial.baseColor, tiledUV).rgb;

    if (texture(phongMaterial.baseColor, tiledUV).a == 0.0) {
        FragColor = vec4(1.0, 0.0, 0.0, 1.0);
    } else {
        FragColor = vec4(lighting * textureColor, 1.0);
    }
}

vec3 calculateAmbientLight() {
    return ambientLight.color * ambientLight.intensity * phongMaterial.ambient;
}

vec3 calculateDirectionalLights(vec3 normal, vec3 viewDir) {
    vec3 totalDiffuseSpec = vec3(0.0);
    
    for (int i = 0; i < numDirectionalLights; i++) {
        vec3 lightDir = normalize(-dirLights[i].direction);
        totalDiffuseSpec += calculateBlinnPhong(
            normal, 
            lightDir, 
            viewDir, 
            dirLights[i].color, 
            dirLights[i].intensity
        );
    }
    
    return totalDiffuseSpec;
}

vec3 calculatePointLights(vec3 normal, vec3 viewDir) {
    vec3 totalDiffuseSpec = vec3(0.0);
    
    for (int i = 0; i < numPointLights; i++) {
        vec3 lightDir = normalize(pointLights[i].position - FragPos);
        float distance = length(pointLights[i].position - FragPos);
        float attenuation = calculateAttenuation(pointLights[i], distance);
        
        vec3 contribution = calculateBlinnPhong(
            normal,
            lightDir,
            viewDir,
            pointLights[i].color,
            pointLights[i].intensity
        );
        
        totalDiffuseSpec += contribution * attenuation;
    }
    
    return totalDiffuseSpec;
}

float calculateAttenuation(PointLight light, float distance) {
    return 1.0 / (
        light.constant + 
        light.linear * distance + 
        light.quadratic * (distance * distance)
    );
}

vec3 calculateBlinnPhong(vec3 normal, vec3 lightDir, vec3 viewDir, vec3 lightColor, float lightIntensity) {
    // Diffuse
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * lightColor * lightIntensity * phongMaterial.diffuse;
    
    // Specular
    vec3 halfwayDir = normalize(lightDir + viewDir);
    float spec = pow(max(dot(normal, halfwayDir), 0.0), phongMaterial.shininess);
    vec3 specular = spec * lightColor * lightIntensity * phongMaterial.specular;
    
    return diffuse + specular;
}
