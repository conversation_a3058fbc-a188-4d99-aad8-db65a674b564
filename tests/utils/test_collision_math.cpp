#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>

#include "utils/collision_math.hpp"
#include "../test_utils.hpp"

using namespace IronFrost;
using namespace IronFrost::CollisionMath;
using Catch::Approx;

TEST_CASE("Plane construction and basic operations", "[utils][collision_math][plane]") {
    SECTION("Plane from normal and distance") {
        glm::vec3 normal(0.0f, 1.0f, 0.0f);
        float distance = 5.0f;
        Plane plane(normal, distance);
        
        REQUIRE(isVec3Equal(plane.normal, normal));
        REQUIRE(plane.distance == Approx(distance));
    }
    
    SECTION("Plane from point and normal") {
        glm::vec3 point(0.0f, 5.0f, 0.0f);
        glm::vec3 normal(0.0f, 1.0f, 0.0f);
        Plane plane(point, normal);
        
        REQUIRE(isVec3Equal(plane.normal, normal));
        REQUIRE(plane.distance == Approx(5.0f));
    }
    
    SECTION("Plane from three points") {
        glm::vec3 p1(0.0f, 0.0f, 0.0f);
        glm::vec3 p2(1.0f, 0.0f, 0.0f);
        glm::vec3 p3(0.0f, 0.0f, 1.0f);
        Plane plane(p1, p2, p3);

        // Should create a plane with normal pointing down (Y-) due to winding order
        REQUIRE(isVec3Equal(plane.normal, glm::vec3(0.0f, -1.0f, 0.0f)));
        REQUIRE(plane.distance == Approx(0.0f));
    }
    
    SECTION("Normal vector is normalized") {
        glm::vec3 unnormalizedNormal(0.0f, 5.0f, 0.0f);
        Plane plane(unnormalizedNormal, 1.0f);
        
        REQUIRE(isVec3Normalized(plane.normal));
        REQUIRE(isVec3Equal(plane.normal, glm::vec3(0.0f, 1.0f, 0.0f)));
    }
}

TEST_CASE("Distance to plane calculations", "[utils][collision_math][plane][distance]") {
    Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
    
    SECTION("Point above plane") {
        glm::vec3 point(0.0f, 5.0f, 0.0f);
        float distance = distanceToPlane(point, horizontalPlane);
        
        REQUIRE(distance == Approx(5.0f));
        REQUIRE(isPointAbovePlane(point, horizontalPlane));
        REQUIRE_FALSE(isPointBelowPlane(point, horizontalPlane));
    }
    
    SECTION("Point below plane") {
        glm::vec3 point(0.0f, -3.0f, 0.0f);
        float distance = distanceToPlane(point, horizontalPlane);
        
        REQUIRE(distance == Approx(-3.0f));
        REQUIRE_FALSE(isPointAbovePlane(point, horizontalPlane));
        REQUIRE(isPointBelowPlane(point, horizontalPlane));
    }
    
    SECTION("Point on plane") {
        glm::vec3 point(10.0f, 0.0f, -5.0f);
        float distance = distanceToPlane(point, horizontalPlane);
        
        REQUIRE(distance == Approx(0.0f));
        REQUIRE_FALSE(isPointAbovePlane(point, horizontalPlane));
        REQUIRE_FALSE(isPointBelowPlane(point, horizontalPlane));
    }
}

TEST_CASE("Point projection onto plane", "[utils][collision_math][plane][projection]") {
    Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
    
    SECTION("Project point above plane") {
        glm::vec3 point(5.0f, 10.0f, -3.0f);
        glm::vec3 projected = projectPointOntoPlane(point, horizontalPlane);
        
        REQUIRE(isVec3Equal(projected, glm::vec3(5.0f, 0.0f, -3.0f)));
    }
    
    SECTION("Project point below plane") {
        glm::vec3 point(2.0f, -7.0f, 8.0f);
        glm::vec3 projected = projectPointOntoPlane(point, horizontalPlane);
        
        REQUIRE(isVec3Equal(projected, glm::vec3(2.0f, 0.0f, 8.0f)));
    }
    
    SECTION("Project point on plane") {
        glm::vec3 point(1.0f, 0.0f, 1.0f);
        glm::vec3 projected = projectPointOntoPlane(point, horizontalPlane);
        
        REQUIRE(isVec3Equal(projected, point));
    }
}

TEST_CASE("Ray-plane intersection", "[utils][collision_math][ray][plane][intersection]") {
    Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
    
    SECTION("Ray intersects plane from above") {
        Ray ray(glm::vec3(0.0f, 5.0f, 0.0f), glm::vec3(0.0f, -1.0f, 0.0f));
        auto result = rayPlaneIntersection(ray, horizontalPlane);
        
        REQUIRE(result.has_value());
        REQUIRE(result.value() == Approx(5.0f));
        
        // Verify intersection point
        glm::vec3 intersectionPoint = ray.origin + result.value() * ray.direction;
        REQUIRE(isVec3Equal(intersectionPoint, glm::vec3(0.0f, 0.0f, 0.0f)));
    }
    
    SECTION("Ray intersects plane from below") {
        Ray ray(glm::vec3(2.0f, -3.0f, 1.0f), glm::vec3(0.0f, 1.0f, 0.0f));
        auto result = rayPlaneIntersection(ray, horizontalPlane);
        
        REQUIRE(result.has_value());
        REQUIRE(result.value() == Approx(3.0f));
        
        // Verify intersection point
        glm::vec3 intersectionPoint = ray.origin + result.value() * ray.direction;
        REQUIRE(isVec3Equal(intersectionPoint, glm::vec3(2.0f, 0.0f, 1.0f)));
    }
    
    SECTION("Ray parallel to plane") {
        Ray ray(glm::vec3(0.0f, 5.0f, 0.0f), glm::vec3(1.0f, 0.0f, 0.0f));
        auto result = rayPlaneIntersection(ray, horizontalPlane);
        
        REQUIRE_FALSE(result.has_value());
    }
    
    SECTION("Ray pointing away from plane") {
        Ray ray(glm::vec3(0.0f, 5.0f, 0.0f), glm::vec3(0.0f, 1.0f, 0.0f));
        auto result = rayPlaneIntersection(ray, horizontalPlane);
        
        REQUIRE_FALSE(result.has_value());
    }
}

TEST_CASE("AABB construction and basic operations", "[utils][collision_math][aabb]") {
    SECTION("AABB from min and max") {
        glm::vec3 min(-1.0f, -2.0f, -3.0f);
        glm::vec3 max(1.0f, 2.0f, 3.0f);
        AABB aabb(min, max);
        
        REQUIRE(isVec3Equal(aabb.min, min));
        REQUIRE(isVec3Equal(aabb.max, max));
    }
    
    SECTION("AABB from center and size") {
        glm::vec3 center(5.0f, 10.0f, -2.0f);
        glm::vec3 size(4.0f, 6.0f, 8.0f);
        AABB aabb = AABB::fromCenterAndSize(center, size);
        
        REQUIRE(isVec3Equal(aabb.getCenter(), center));
        REQUIRE(isVec3Equal(aabb.getSize(), size));
        REQUIRE(isVec3Equal(aabb.min, glm::vec3(3.0f, 7.0f, -6.0f)));
        REQUIRE(isVec3Equal(aabb.max, glm::vec3(7.0f, 13.0f, 2.0f)));
    }
}

TEST_CASE("Point in AABB tests", "[utils][collision_math][aabb][point]") {
    AABB aabb(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));
    
    SECTION("Point inside AABB") {
        REQUIRE(isPointInAABB(glm::vec3(0.0f, 0.0f, 0.0f), aabb));
        REQUIRE(isPointInAABB(glm::vec3(0.5f, -0.5f, 0.8f), aabb));
    }
    
    SECTION("Point on AABB boundary") {
        REQUIRE(isPointInAABB(glm::vec3(1.0f, 0.0f, 0.0f), aabb));
        REQUIRE(isPointInAABB(glm::vec3(-1.0f, 1.0f, -1.0f), aabb));
    }
    
    SECTION("Point outside AABB") {
        REQUIRE_FALSE(isPointInAABB(glm::vec3(2.0f, 0.0f, 0.0f), aabb));
        REQUIRE_FALSE(isPointInAABB(glm::vec3(0.0f, -2.0f, 0.0f), aabb));
        REQUIRE_FALSE(isPointInAABB(glm::vec3(0.0f, 0.0f, 2.0f), aabb));
    }
}

TEST_CASE("AABB intersection tests", "[utils][collision_math][aabb][intersection]") {
    AABB aabb1(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));

    SECTION("Overlapping AABBs") {
        AABB aabb2(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec3(2.0f, 2.0f, 2.0f));
        REQUIRE(aabbIntersection(aabb1, aabb2));
        REQUIRE(aabbIntersection(aabb2, aabb1)); // Should be symmetric
    }

    SECTION("Non-overlapping AABBs") {
        AABB aabb2(glm::vec3(2.0f, 2.0f, 2.0f), glm::vec3(3.0f, 3.0f, 3.0f));
        REQUIRE_FALSE(aabbIntersection(aabb1, aabb2));
        REQUIRE_FALSE(aabbIntersection(aabb2, aabb1)); // Should be symmetric
    }

    SECTION("Touching AABBs") {
        AABB aabb2(glm::vec3(1.0f, -1.0f, -1.0f), glm::vec3(2.0f, 1.0f, 1.0f));
        REQUIRE(aabbIntersection(aabb1, aabb2)); // Touching counts as intersection
    }
}

TEST_CASE("Distance calculations", "[utils][collision_math][distance]") {
    AABB aabb(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));

    SECTION("Distance to AABB from outside") {
        glm::vec3 point(3.0f, 0.0f, 0.0f);
        float distance = distanceToAABB(point, aabb);
        REQUIRE(distance == Approx(2.0f)); // Distance from (3,0,0) to (1,0,0)
    }

    SECTION("Distance to AABB from inside") {
        glm::vec3 point(0.0f, 0.0f, 0.0f); // Center of AABB
        float distance = distanceToAABB(point, aabb);
        REQUIRE(distance == Approx(0.0f)); // Point is inside
    }

    SECTION("Closest point on AABB") {
        glm::vec3 point(3.0f, 2.0f, -2.0f);
        glm::vec3 closest = closestPointOnAABB(point, aabb);
        REQUIRE(isVec3Equal(closest, glm::vec3(1.0f, 1.0f, -1.0f)));
    }
}
