#define GLM_ENABLE_EXPERIMENTAL

#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_floating_point.hpp>

#include "scene/scene_graph/scene_entities.hpp"
#include "scene/scene_graph/scene_node.hpp"
#include "test_utils.hpp"

#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>
#include <glm/gtc/quaternion.hpp>
#include <glm/gtx/matrix_decompose.hpp>
#include <cmath>
#include <vector>

using namespace IronFrost;
using Catch::Matchers::WithinAbs;

TEST_CASE("SceneNode construction and initialization", "[scene][scene_node][construction]") {
    SECTION("Default constructor creates identity node") {
        SceneNode node;
        
        REQUIRE(isVec3Equal(node.getPosition(), glm::vec3(0.0f)));
        REQUIRE(isQuatEqual(node.getRotation(), glm::identity<glm::quat>()));
        REQUIRE(isVec3Equal(node.getScale(), glm::vec3(1.0f)));
        REQUIRE(node.visible == true);
        REQUIRE(node.children.empty());
        REQUIRE(node.sceneObject == std::nullopt);
        REQUIRE(node.sceneModel == std::nullopt);
        REQUIRE(node.sceneLight == std::nullopt);
    }
    
    SECTION("Parameterized constructor sets values correctly") {
        glm::vec3 position(1.0f, 2.0f, 3.0f);
        glm::quat rotation = glm::angleAxis(glm::radians(45.0f), glm::vec3(0.0f, 1.0f, 0.0f));
        glm::vec3 scale(2.0f, 3.0f, 4.0f);
        
        SceneNode node(position, rotation, scale);
        
        REQUIRE(isVec3Equal(node.getPosition(), position));
        REQUIRE(isQuatEqual(node.getRotation(), rotation));
        REQUIRE(isVec3Equal(node.getScale(), scale));
        REQUIRE(node.visible == true);
        REQUIRE(node.children.empty());
    }
}

TEST_CASE("SceneNode transform properties", "[scene][scene_node][transform]") {
    SceneNode node;
    
    SECTION("Position getter and setter") {
        glm::vec3 newPosition(5.0f, -2.0f, 10.0f);
        node.setPosition(newPosition);
        
        REQUIRE(isVec3Equal(node.getPosition(), newPosition));
    }
    
    SECTION("Rotation getter and setter") {
        glm::quat newRotation = glm::angleAxis(glm::radians(90.0f), glm::vec3(1.0f, 0.0f, 0.0f));
        node.setRotation(newRotation);
        
        REQUIRE(isQuatEqual(node.getRotation(), newRotation));
    }
    
    SECTION("Scale getter and setter") {
        glm::vec3 newScale(0.5f, 2.0f, 1.5f);
        node.setScale(newScale);
        
        REQUIRE(isVec3Equal(node.getScale(), newScale));
    }
    
    SECTION("Multiple property changes") {
        glm::vec3 position(1.0f, 2.0f, 3.0f);
        glm::quat rotation = glm::angleAxis(glm::radians(45.0f), glm::vec3(0.0f, 1.0f, 0.0f));
        glm::vec3 scale(2.0f, 2.0f, 2.0f);
        
        node.setPosition(position);
        node.setRotation(rotation);
        node.setScale(scale);
        
        REQUIRE(isVec3Equal(node.getPosition(), position));
        REQUIRE(isQuatEqual(node.getRotation(), rotation));
        REQUIRE(isVec3Equal(node.getScale(), scale));
    }
}

TEST_CASE("SceneNode transform matrix computation", "[scene][scene_node][matrix]") {
    SECTION("Identity transform produces identity matrix") {
        SceneNode node;
        glm::mat4 transform = node.getTransform();
        
        REQUIRE(isMat4Equal(transform, glm::mat4(1.0f)));
    }
    
    SECTION("Translation only") {
        SceneNode node;
        glm::vec3 position(5.0f, 3.0f, -2.0f);
        node.setPosition(position);
        
        glm::mat4 transform = node.getTransform();
        glm::mat4 expected = glm::translate(glm::mat4(1.0f), position);
        
        REQUIRE(isMat4Equal(transform, expected));
    }
    
    SECTION("Rotation only") {
        SceneNode node;
        glm::quat rotation = glm::angleAxis(glm::radians(90.0f), glm::vec3(0.0f, 1.0f, 0.0f));
        node.setRotation(rotation);
        
        glm::mat4 transform = node.getTransform();
        glm::mat4 expected = glm::mat4_cast(rotation);
        
        REQUIRE(isMat4Equal(transform, expected));
    }
    
    SECTION("Scale only") {
        SceneNode node;
        glm::vec3 scale(2.0f, 3.0f, 0.5f);
        node.setScale(scale);
        
        glm::mat4 transform = node.getTransform();
        glm::mat4 expected = glm::scale(glm::mat4(1.0f), scale);
        
        REQUIRE(isMat4Equal(transform, expected));
    }
    
    SECTION("Combined transform (TRS order)") {
        SceneNode node;
        glm::vec3 position(1.0f, 2.0f, 3.0f);
        glm::quat rotation = glm::angleAxis(glm::radians(45.0f), glm::vec3(0.0f, 1.0f, 0.0f));
        glm::vec3 scale(2.0f, 2.0f, 2.0f);
        
        node.setPosition(position);
        node.setRotation(rotation);
        node.setScale(scale);
        
        glm::mat4 transform = node.getTransform();
        
        // Expected: T * R * S
        glm::mat4 expected = glm::translate(glm::mat4(1.0f), position) * 
                            glm::mat4_cast(rotation) * 
                            glm::scale(glm::mat4(1.0f), scale);
        
        REQUIRE(isMat4Equal(transform, expected));
    }
    
    SECTION("Transform with parent transform") {
        SceneNode node;
        glm::vec3 position(1.0f, 0.0f, 0.0f);
        node.setPosition(position);
        
        glm::mat4 parentTransform = glm::translate(glm::mat4(1.0f), glm::vec3(2.0f, 0.0f, 0.0f));
        glm::mat4 transform = node.getTransform(parentTransform);
        
        // Should be parent * local
        glm::mat4 localTransform = glm::translate(glm::mat4(1.0f), position);
        glm::mat4 expected = parentTransform * localTransform;
        
        REQUIRE(isMat4Equal(transform, expected));
    }
}

TEST_CASE("SceneNode transform caching and dirty flag", "[scene][scene_node][caching]") {
    SECTION("Transform is cached after first computation") {
        SceneNode node;
        node.setPosition(glm::vec3(1.0f, 2.0f, 3.0f));
        
        // First call computes and caches
        glm::mat4 transform1 = node.getTransform();
        
        // Second call should return cached result
        glm::mat4 transform2 = node.getTransform();
        
        REQUIRE(isMat4Equal(transform1, transform2));
    }
    
    SECTION("Setting position marks transform dirty") {
        SceneNode node;
        glm::mat4 originalTransform = node.getTransform();
        
        node.setPosition(glm::vec3(5.0f, 0.0f, 0.0f));
        glm::mat4 newTransform = node.getTransform();
        
        REQUIRE_FALSE(isMat4Equal(originalTransform, newTransform));
    }
    
    SECTION("Setting rotation marks transform dirty") {
        SceneNode node;
        glm::mat4 originalTransform = node.getTransform();
        
        node.setRotation(glm::angleAxis(glm::radians(90.0f), glm::vec3(1.0f, 0.0f, 0.0f)));
        glm::mat4 newTransform = node.getTransform();
        
        REQUIRE_FALSE(isMat4Equal(originalTransform, newTransform));
    }
    
    SECTION("Setting scale marks transform dirty") {
        SceneNode node;
        glm::mat4 originalTransform = node.getTransform();
        
        node.setScale(glm::vec3(2.0f, 2.0f, 2.0f));
        glm::mat4 newTransform = node.getTransform();
        
        REQUIRE_FALSE(isMat4Equal(originalTransform, newTransform));
    }
}

TEST_CASE("SceneNode hierarchy and children", "[scene][scene_node][hierarchy]") {
    SECTION("Node starts with no children") {
        SceneNode parent;
        REQUIRE(parent.children.empty());
        REQUIRE(parent.children.size() == 0);
    }
    
    SECTION("Adding children") {
        SceneNode parent;

        // Use emplace_back to construct children in place
        parent.children.emplace_back();
        parent.children.emplace_back();

        parent.children[0].setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        parent.children[1].setPosition(glm::vec3(0.0f, 1.0f, 0.0f));

        REQUIRE(parent.children.size() == 2);
        REQUIRE(isVec3Equal(parent.children[0].getPosition(), glm::vec3(1.0f, 0.0f, 0.0f)));
        REQUIRE(isVec3Equal(parent.children[1].getPosition(), glm::vec3(0.0f, 1.0f, 0.0f)));
    }
    
    SECTION("Nested hierarchy") {
        SceneNode root;

        root.setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        root.children.emplace_back();
        root.children[0].setPosition(glm::vec3(0.0f, 1.0f, 0.0f));
        root.children[0].children.emplace_back();
        root.children[0].children[0].setPosition(glm::vec3(0.0f, 0.0f, 1.0f));

        REQUIRE(root.children.size() == 1);
        REQUIRE(root.children[0].children.size() == 1);
        REQUIRE(isVec3Equal(root.children[0].children[0].getPosition(), glm::vec3(0.0f, 0.0f, 1.0f)));
    }
}

TEST_CASE("SceneNode scene entities", "[scene][scene_node][entities]") {
    SECTION("Default node has no scene entities") {
        SceneNode node;

        REQUIRE(node.sceneObject == std::nullopt);
        REQUIRE(node.sceneModel == std::nullopt);
        REQUIRE(node.sceneLight == std::nullopt);
    }

    SECTION("SceneObject assignment and access") {
        SceneNode node;
        SceneObject obj;
        obj.renderableObjectID = 42;
        obj.uvTiling = glm::vec2(2.0f, 3.0f);

        node.sceneObject = obj;

        REQUIRE(node.sceneObject.has_value());
        REQUIRE(node.sceneObject->renderableObjectID == 42);
        REQUIRE(isVec3Equal(glm::vec3(node.sceneObject->uvTiling, 0.0f), glm::vec3(2.0f, 3.0f, 0.0f)));
    }

    SECTION("SceneModel assignment and access") {
        SceneNode node;
        SceneModel model;
        model.renderableModelID = 123;

        node.sceneModel = model;

        REQUIRE(node.sceneModel.has_value());
        REQUIRE(node.sceneModel->renderableModelID == 123);
    }

    SECTION("SceneLight assignment and access") {
        SceneNode node;
        SceneLight light;
        light.color = glm::vec3(1.0f, 0.5f, 0.2f);
        light.intensity = 2.5f;
        light.constant = 1.0f;
        light.linear = 0.09f;
        light.quadratic = 0.032f;

        node.sceneLight = light;

        REQUIRE(node.sceneLight.has_value());
        REQUIRE(isVec3Equal(node.sceneLight->color, glm::vec3(1.0f, 0.5f, 0.2f)));
        REQUIRE_THAT(node.sceneLight->intensity, WithinAbs(2.5f, 0.001f));
        REQUIRE_THAT(node.sceneLight->constant, WithinAbs(1.0f, 0.001f));
        REQUIRE_THAT(node.sceneLight->linear, WithinAbs(0.09f, 0.001f));
        REQUIRE_THAT(node.sceneLight->quadratic, WithinAbs(0.032f, 0.001f));
    }

    SECTION("Multiple entities on same node") {
        SceneNode node;

        SceneObject obj;
        obj.renderableObjectID = 1;
        obj.uvTiling = glm::vec2(1.0f, 1.0f);

        SceneLight light;
        light.color = glm::vec3(1.0f, 1.0f, 1.0f);
        light.intensity = 1.0f;

        node.sceneObject = obj;
        node.sceneLight = light;

        REQUIRE(node.sceneObject.has_value());
        REQUIRE(node.sceneLight.has_value());
        REQUIRE(node.sceneModel == std::nullopt);
    }

    SECTION("Entity removal by setting to nullopt") {
        SceneNode node;
        SceneObject obj;
        obj.renderableObjectID = 1;

        node.sceneObject = obj;
        REQUIRE(node.sceneObject.has_value());

        node.sceneObject = std::nullopt;
        REQUIRE(node.sceneObject == std::nullopt);
    }
}

TEST_CASE("SceneNode visibility", "[scene][scene_node][visibility]") {
    SECTION("Node is visible by default") {
        SceneNode node;
        REQUIRE(node.visible == true);
    }

    SECTION("Visibility can be changed") {
        SceneNode node;

        node.visible = false;
        REQUIRE(node.visible == false);

        node.visible = true;
        REQUIRE(node.visible == true);
    }

    SECTION("Visibility is independent per node") {
        SceneNode parent;
        SceneNode child;

        parent.visible = true;
        parent.children.emplace_back();
        parent.children[0].visible = false;

        REQUIRE(parent.visible == true);
        REQUIRE(parent.children[0].visible == false);
    }
}

TEST_CASE("SceneNode traversal", "[scene][scene_node][traversal]") {
    SECTION("Single node traversal") {
        SceneNode node;
        node.setPosition(glm::vec3(1.0f, 2.0f, 3.0f));

        int callCount = 0;
        glm::mat4 receivedTransform;
        SceneNode* receivedNode = nullptr;

        node.traverse(glm::mat4(1.0f), [&](SceneNode& n, const glm::mat4& transform) {
            callCount++;
            receivedTransform = transform;
            receivedNode = &n;
        });

        REQUIRE(callCount == 1);
        REQUIRE(receivedNode == &node);
        REQUIRE(isMat4Equal(receivedTransform, node.getTransform()));
    }

    SECTION("Parent-child traversal order") {
        SceneNode parent;

        parent.setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        parent.children.emplace_back();
        parent.children.emplace_back();
        parent.children[0].setPosition(glm::vec3(0.0f, 1.0f, 0.0f));
        parent.children[1].setPosition(glm::vec3(0.0f, 0.0f, 1.0f));

        std::vector<SceneNode*> visitOrder;
        std::vector<glm::mat4> transforms;

        parent.traverse(glm::mat4(1.0f), [&](SceneNode& node, const glm::mat4& transform) {
            visitOrder.push_back(&node);
            transforms.push_back(transform);
        });

        REQUIRE(visitOrder.size() == 3);
        REQUIRE(visitOrder[0] == &parent);
        REQUIRE(visitOrder[1] == &parent.children[0]);
        REQUIRE(visitOrder[2] == &parent.children[1]);
    }

    SECTION("Deep hierarchy traversal") {
        SceneNode root;

        root.setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        root.children.emplace_back();
        root.children[0].setPosition(glm::vec3(0.0f, 1.0f, 0.0f));
        root.children[0].children.emplace_back();
        root.children[0].children[0].setPosition(glm::vec3(0.0f, 0.0f, 1.0f));

        std::vector<SceneNode*> visitOrder;

        root.traverse(glm::mat4(1.0f), [&](SceneNode& node, const glm::mat4& transform) {
            visitOrder.push_back(&node);
        });

        REQUIRE(visitOrder.size() == 3);
        REQUIRE(visitOrder[0] == &root);
        REQUIRE(visitOrder[1] == &root.children[0]);
        REQUIRE(visitOrder[2] == &root.children[0].children[0]);
    }

    SECTION("Transform propagation in traversal") {
        SceneNode parent;

        parent.setPosition(glm::vec3(2.0f, 0.0f, 0.0f));
        parent.children.emplace_back();
        parent.children[0].setPosition(glm::vec3(1.0f, 0.0f, 0.0f));

        std::vector<glm::mat4> transforms;

        parent.traverse(glm::mat4(1.0f), [&](SceneNode& node, const glm::mat4& transform) {
            transforms.push_back(transform);
        });

        REQUIRE(transforms.size() == 2);

        // Parent transform should be just its local transform
        glm::mat4 expectedParentTransform = glm::translate(glm::mat4(1.0f), glm::vec3(2.0f, 0.0f, 0.0f));
        REQUIRE(isMat4Equal(transforms[0], expectedParentTransform));

        // Child transform should be parent * child
        glm::mat4 expectedChildTransform = expectedParentTransform * glm::translate(glm::mat4(1.0f), glm::vec3(1.0f, 0.0f, 0.0f));
        REQUIRE(isMat4Equal(transforms[1], expectedChildTransform));
    }

    SECTION("Traversal with custom parent transform") {
        SceneNode node;
        node.setPosition(glm::vec3(1.0f, 0.0f, 0.0f));

        glm::mat4 customParent = glm::translate(glm::mat4(1.0f), glm::vec3(5.0f, 0.0f, 0.0f));
        glm::mat4 receivedTransform;

        node.traverse(customParent, [&](SceneNode& n, const glm::mat4& transform) {
            receivedTransform = transform;
        });

        glm::mat4 expected = customParent * glm::translate(glm::mat4(1.0f), glm::vec3(1.0f, 0.0f, 0.0f));
        REQUIRE(isMat4Equal(receivedTransform, expected));
    }
}

TEST_CASE("SceneNode dirty flag propagation", "[scene][scene_node][dirty_propagation]") {
    SECTION("Parent change affects child transforms") {
        SceneNode parent;

        parent.setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        parent.children.emplace_back();
        parent.children[0].setPosition(glm::vec3(1.0f, 0.0f, 0.0f));

        // Get initial transforms to cache them
        glm::mat4 parentTransform1 = parent.getTransform();
        glm::mat4 childTransform1 = parent.children[0].getTransform(parentTransform1);

        // Change parent position
        parent.setPosition(glm::vec3(2.0f, 0.0f, 0.0f));

        // Child should get new transform even though its local transform didn't change
        glm::mat4 parentTransform2 = parent.getTransform();
        glm::mat4 childTransform2 = parent.children[0].getTransform(parentTransform2);

        REQUIRE_FALSE(isMat4Equal(parentTransform1, parentTransform2));
        REQUIRE_FALSE(isMat4Equal(childTransform1, childTransform2));
    }

    SECTION("Deep hierarchy dirty propagation") {
        SceneNode root;

        root.setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        root.children.emplace_back();
        root.children[0].setPosition(glm::vec3(1.0f, 0.0f, 0.0f));
        root.children[0].children.emplace_back();
        root.children[0].children[0].setPosition(glm::vec3(1.0f, 0.0f, 0.0f));

        // Cache initial transforms
        std::vector<glm::mat4> initialTransforms;
        root.traverse(glm::mat4(1.0f), [&](SceneNode& node, const glm::mat4& transform) {
            initialTransforms.push_back(transform);
        });

        // Change root position
        root.setPosition(glm::vec3(5.0f, 0.0f, 0.0f));

        // All transforms should be different
        std::vector<glm::mat4> newTransforms;
        root.traverse(glm::mat4(1.0f), [&](SceneNode& node, const glm::mat4& transform) {
            newTransforms.push_back(transform);
        });

        REQUIRE(initialTransforms.size() == newTransforms.size());
        for (size_t i = 0; i < initialTransforms.size(); ++i) {
            REQUIRE_FALSE(isMat4Equal(initialTransforms[i], newTransforms[i]));
        }
    }
}

TEST_CASE("SceneNode edge cases and robustness", "[scene][scene_node][edge_cases]") {
    SECTION("Zero scale handling") {
        SceneNode node;
        node.setScale(glm::vec3(0.0f, 0.0f, 0.0f));

        glm::mat4 transform = node.getTransform();

        // Should not crash and should produce a valid matrix
        REQUIRE(std::isfinite(transform[0][0]));
        REQUIRE(std::isfinite(transform[1][1]));
        REQUIRE(std::isfinite(transform[2][2]));
    }

    SECTION("Negative scale handling") {
        SceneNode node;
        node.setScale(glm::vec3(-1.0f, -2.0f, -0.5f));

        glm::mat4 transform = node.getTransform();
        glm::mat4 expected = glm::scale(glm::mat4(1.0f), glm::vec3(-1.0f, -2.0f, -0.5f));

        REQUIRE(isMat4Equal(transform, expected));
    }

    SECTION("Very large values") {
        SceneNode node;
        glm::vec3 largePosition(1000000.0f, -1000000.0f, 500000.0f);
        glm::vec3 largeScale(1000.0f, 1000.0f, 1000.0f);

        node.setPosition(largePosition);
        node.setScale(largeScale);

        glm::mat4 transform = node.getTransform();

        // Should not produce NaN or infinite values
        for (int i = 0; i < 4; ++i) {
            for (int j = 0; j < 4; ++j) {
                REQUIRE(std::isfinite(transform[i][j]));
            }
        }
    }

    SECTION("Empty traversal callback") {
        SceneNode node;

        // Should not crash with empty callback
        node.traverse(glm::mat4(1.0f), [](SceneNode&, const glm::mat4&) {
            // Empty callback
        });

        SUCCEED("Empty callback traversal completed without crash");
    }

    SECTION("Many children performance") {
        SceneNode parent;

        // Add many children
        for (int i = 0; i < 100; ++i) {
            parent.children.emplace_back();
            parent.children.back().setPosition(glm::vec3(static_cast<float>(i), 0.0f, 0.0f));
        }

        int visitCount = 0;
        parent.traverse(glm::mat4(1.0f), [&](SceneNode&, const glm::mat4&) {
            visitCount++;
        });

        REQUIRE(visitCount == 101); // Parent + 100 children
    }
}
