#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>

#include "scene/collision/shapes/collision_shapes.hpp"
#include "../test_utils.hpp"

using namespace IronFrost;
using Catch::Approx;

TEST_CASE("PlaneCollisionShape basic functionality", "[scene][collision_shapes][plane]") {
    // Create a horizontal plane at Y=0 with normal pointing up
    PlaneCollisionShape plane(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec3(0.0f, 1.0f, 0.0f));
    
    SECTION("Point above plane - no collision") {
        glm::vec3 point(0.0f, 5.0f, 0.0f);
        auto collision = plane.checkPointCollision(point);
        REQUIRE_FALSE(collision.has_value());
    }
    
    SECTION("Point below plane - collision detected") {
        glm::vec3 point(0.0f, -3.0f, 0.0f);
        auto collision = plane.checkPointCollision(point);
        REQUIRE(collision.has_value());
        REQUIRE(isVec3Equal(collision.value(), glm::vec3(0.0f, 1.0f, 0.0f)));
    }
    
    SECTION("Point on plane - no collision") {
        glm::vec3 point(10.0f, 0.0f, -5.0f);
        auto collision = plane.checkPointCollision(point);
        REQUIRE_FALSE(collision.has_value());
    }
    
    SECTION("Closest point calculation") {
        glm::vec3 point(5.0f, 10.0f, -3.0f);
        glm::vec3 closest = plane.getClosestPoint(point);
        REQUIRE(isVec3Equal(closest, glm::vec3(5.0f, 0.0f, -3.0f)));
    }
    
    SECTION("Distance to surface") {
        REQUIRE(plane.getDistanceToSurface(glm::vec3(0.0f, 5.0f, 0.0f)) == Approx(5.0f));
        REQUIRE(plane.getDistanceToSurface(glm::vec3(0.0f, -3.0f, 0.0f)) == Approx(-3.0f));
        REQUIRE(plane.getDistanceToSurface(glm::vec3(10.0f, 0.0f, -5.0f)) == Approx(0.0f));
    }
}

TEST_CASE("AABBCollisionShape basic functionality", "[scene][collision_shapes][aabb]") {
    AABBCollisionShape aabb(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));
    
    SECTION("Point outside AABB - no collision") {
        glm::vec3 point(2.0f, 0.0f, 0.0f);
        auto collision = aabb.checkPointCollision(point);
        REQUIRE_FALSE(collision.has_value());
    }
    
    SECTION("Point inside AABB - collision detected") {
        glm::vec3 point(0.0f, 0.0f, 0.0f);
        auto collision = aabb.checkPointCollision(point);
        REQUIRE(collision.has_value());
        // Should return a valid normal vector
        REQUIRE(isVec3Normalized(collision.value()));
    }
    
    SECTION("Point on AABB boundary - collision detected") {
        glm::vec3 point(1.0f, 0.0f, 0.0f);
        auto collision = aabb.checkPointCollision(point);
        REQUIRE(collision.has_value());
        REQUIRE(isVec3Equal(collision.value(), glm::vec3(1.0f, 0.0f, 0.0f)));
    }
    
    SECTION("Closest point calculation") {
        glm::vec3 point(3.0f, 2.0f, -2.0f);
        glm::vec3 closest = aabb.getClosestPoint(point);
        REQUIRE(isVec3Equal(closest, glm::vec3(1.0f, 1.0f, -1.0f)));
    }
    
    SECTION("Distance to surface - outside") {
        glm::vec3 point(3.0f, 0.0f, 0.0f);
        float distance = aabb.getDistanceToSurface(point);
        REQUIRE(distance == Approx(2.0f));
    }
    
    SECTION("Distance to surface - inside") {
        glm::vec3 point(0.0f, 0.0f, 0.0f);
        float distance = aabb.getDistanceToSurface(point);
        REQUIRE(distance <= 0.0f); // Should be negative or zero for inside
    }
}

TEST_CASE("SphereCollisionShape basic functionality", "[scene][collision_shapes][sphere]") {
    SphereCollisionShape sphere(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f);

    SECTION("Point collision detection") {
        // Point inside sphere
        glm::vec3 pointInside(1.0f, 0.0f, 0.0f);
        auto collision = sphere.checkPointCollision(pointInside);
        REQUIRE(collision.has_value());
        REQUIRE(isVec3Equal(collision.value(), glm::vec3(1.0f, 0.0f, 0.0f))); // Normal pointing outward

        // Point outside sphere
        glm::vec3 pointOutside(3.0f, 0.0f, 0.0f);
        collision = sphere.checkPointCollision(pointOutside);
        REQUIRE_FALSE(collision.has_value());

        // Point on sphere surface
        glm::vec3 pointOnSurface(2.0f, 0.0f, 0.0f);
        collision = sphere.checkPointCollision(pointOnSurface);
        REQUIRE(collision.has_value());
    }

    SECTION("Sphere collision detection") {
        // Intersecting spheres
        CollisionMath::Sphere otherSphere(glm::vec3(3.0f, 0.0f, 0.0f), 2.0f);
        auto collision = sphere.checkSphereCollision(otherSphere);
        REQUIRE(collision.has_value());
        REQUIRE(isVec3Equal(collision.value(), glm::vec3(1.0f, 0.0f, 0.0f))); // Normal pointing from sphere to otherSphere

        // Non-intersecting spheres
        CollisionMath::Sphere farSphere(glm::vec3(5.0f, 0.0f, 0.0f), 1.0f);
        collision = sphere.checkSphereCollision(farSphere);
        REQUIRE_FALSE(collision.has_value());

        // Touching spheres
        CollisionMath::Sphere touchingSphere(glm::vec3(4.0f, 0.0f, 0.0f), 2.0f);
        collision = sphere.checkSphereCollision(touchingSphere);
        REQUIRE(collision.has_value());
    }

    SECTION("Closest point calculation") {
        // Point inside sphere
        glm::vec3 pointInside(1.0f, 0.0f, 0.0f);
        glm::vec3 closest = sphere.getClosestPoint(pointInside);
        REQUIRE(isVec3Equal(closest, pointInside)); // Point inside returns itself

        // Point outside sphere
        glm::vec3 pointOutside(4.0f, 0.0f, 0.0f);
        closest = sphere.getClosestPoint(pointOutside);
        REQUIRE(isVec3Equal(closest, glm::vec3(2.0f, 0.0f, 0.0f))); // Closest point on surface
    }

    SECTION("Distance to surface") {
        // Point inside sphere
        glm::vec3 pointInside(1.0f, 0.0f, 0.0f);
        float distance = sphere.getDistanceToSurface(pointInside);
        REQUIRE(distance == Approx(-1.0f)); // Negative distance for inside

        // Point outside sphere
        glm::vec3 pointOutside(4.0f, 0.0f, 0.0f);
        distance = sphere.getDistanceToSurface(pointOutside);
        REQUIRE(distance == Approx(2.0f)); // Positive distance for outside

        // Point on surface
        glm::vec3 pointOnSurface(2.0f, 0.0f, 0.0f);
        distance = sphere.getDistanceToSurface(pointOnSurface);
        REQUIRE(distance == Approx(0.0f)); // Zero distance for on surface
    }

    SECTION("Sphere collision resolution") {
        // Intersecting sphere that needs resolution
        CollisionMath::Sphere intersectingSphere(glm::vec3(1.0f, 0.0f, 0.0f), 1.5f);
        glm::vec3 resolved = sphere.resolveSphereCollision(intersectingSphere);

        // Should be pushed away from the collision sphere
        REQUIRE(resolved.x > intersectingSphere.center.x); // Moved further in positive X

        // Non-intersecting sphere should not be moved
        CollisionMath::Sphere farSphere(glm::vec3(5.0f, 0.0f, 0.0f), 1.0f);
        resolved = sphere.resolveSphereCollision(farSphere);
        REQUIRE(isVec3Equal(resolved, farSphere.center)); // No change
    }
}


